package com.example.recoverdev.utils

import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.FolderCategory
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.data.model.ScanResult
import java.io.File

object FolderCategoryUtils {
    
    fun createScanResult(files: List<RecoverableFile>, fileType: FileType): ScanResult {
        val folderCategories = groupFilesByFolder(files)
        
        return ScanResult(
            fileType = fileType,
            totalFiles = files.size,
            allFiles = files,
            folderCategories = folderCategories
        )
    }
    
    fun groupFilesByFolder(files: List<RecoverableFile>): List<FolderCategory> {
        val folderMap = mutableMapOf<String, MutableList<RecoverableFile>>()
        
        files.forEach { file ->
            val folderPath = getFolderPath(file.path)
            val folderName = getFolderDisplayName(folderPath)
            
            if (!folderMap.containsKey(folderName)) {
                folderMap[folderName] = mutableListOf()
            }
            folderMap[folderName]?.add(file)
        }
        
        return folderMap.map { (folderName, folderFiles) ->
            FolderCategory(
                id = folderName.lowercase().replace(" ", "_"),
                name = folderName,
                path = getFolderPath(folderFiles.first().path),
                fileCount = folderFiles.size,
                files = folderFiles.sortedByDescending { it.dateModified },
                isSpecialFolder = isSpecialFolder(folderName)
            )
        }.sortedWith(compareBy<FolderCategory> { !it.isSpecialFolder }.thenByDescending { it.fileCount })
    }
    
    private fun getFolderPath(filePath: String): String {
        return File(filePath).parent ?: "/"
    }
    
    private fun getFolderDisplayName(folderPath: String): String {
        val folderName = File(folderPath).name
        
        return when {
            folderPath.contains("thumbnails", ignoreCase = true) -> "thumbnails"
            folderPath.contains("cache", ignoreCase = true) -> "Cache"
            folderPath.contains("temp", ignoreCase = true) -> "Temporary"
            folderPath.contains("recent", ignoreCase = true) -> "Recent"
            folderPath.startsWith(".") -> "Hidden Files"
            folderName.isEmpty() -> "Root"
            folderName == "DCIM" -> "Camera"
            folderName == "Pictures" -> "Pictures"
            folderName == "Download" -> "Downloads"
            folderName == "Screenshots" -> "Screenshots"
            folderName == "WhatsApp" -> "WhatsApp"
            folderName == "Instagram" -> "Instagram"
            else -> folderName
        }
    }
    
    private fun isSpecialFolder(folderName: String): Boolean {
        return folderName.lowercase() in listOf(
            "thumbnails", "cache", "temporary", "recent", "hidden files"
        )
    }
}