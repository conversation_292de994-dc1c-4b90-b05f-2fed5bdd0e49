package com.example.recoverdev.utils

import com.tencent.mmkv.MMKV

object PreferenceUtils {
    
    private const val KEY_FIRST_LAUNCH = "first_launch"
    private const val KEY_GUIDE_SHOWN = "guide_shown"
    private const val KEY_TERMS_AGREED = "terms_agreed"
    private const val KEY_SELECTED_LANGUAGE = "selected_language"
    
    private val mmkv: MMKV by lazy {
        MMKV.defaultMMKV()
    }

    fun isFirstLaunch(): Boolean {
        return mmkv.decodeBool(KEY_FIRST_LAUNCH, true)
    }
    fun setFirstLaunchComplete() {
        mmkv.encode(KEY_FIRST_LAUNCH, false)
    }
    fun isGuideShown(): Boolean {
        return mmkv.decodeBool(KEY_GUIDE_SHOWN, false)
    }
    fun setGuideShown() {
        mmkv.encode(KEY_GUIDE_SHOWN, true)
    }

    fun hasAgreedToTerms(): Boolean {
        return mmkv.decodeBool(KEY_TERMS_AGREED, false)
    }

    fun setTermsAgreed() {
        mmkv.encode(KEY_TERMS_AGREED, true)
    }

    fun clearTermsAgreement() {
        mmkv.remove(KEY_TERMS_AGREED)
    }

    fun getCurrentLanguage(): String {
        return mmkv.decodeString(KEY_SELECTED_LANGUAGE, "English") ?: "English"
    }

    fun setLanguage(language: String) {
        mmkv.encode(KEY_SELECTED_LANGUAGE, language)
    }
}