package com.example.recoverdev.utils

import kotlin.math.log10
import kotlin.math.pow

/**
 * File-related utility functions
 */
object FileUtils {
    
    /**
     * Format file size for display
     * @param bytes File size in bytes
     * @return Formatted file size string
     */
    fun formatFileSize(bytes: Long): String {
        if (bytes <= 0) return "0 B"
        
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (log10(bytes.toDouble()) / log10(1024.0)).toInt()
        
        return String.format(
            "%.1f %s",
            bytes / 1024.0.pow(digitGroups.toDouble()),
            units[digitGroups]
        )
    }
    
    /**
     * Clean special suffixes of deleted files to restore original file name
     * Supports naming rules for deleted files on various phone brands
     * @param fileName Original file name with potential deleted file suffixes
     * @return Cleaned file name without manufacturer-specific suffixes
     */
    fun cleanDeletedFileName(fileName: String): String {
        if (fileName.isBlank()) return ""
        var cleanName = fileName
        
        // OPPO/OnePlus ColorOS deleted file patterns
        val oppoPatterns = listOf(
            "_temp",
            "_deleted", 
            "_trash",
            "_recycle",
            "_backup"
        )
        
        // Xiaomi MIUI deleted file patterns
        val miuiPatterns = listOf(
            ".trashed",
            "_trashed",
            ".mi_deleted",
            "_mi_deleted",
            ".deleted"
        )
        
        // Huawei EMUI/HarmonyOS deleted file patterns
        val huaweiPatterns = listOf(
            ".hwdeleted",
            "_hwdeleted",
            ".huawei_trash",
            "_huawei_trash",
            ".emui_deleted"
        )
        
        // Vivo FuntouchOS/OriginOS deleted file patterns
        val vivoPatterns = listOf(
            ".vivo_deleted",
            "_vivo_deleted",
            ".funtouch_trash",
            "_funtouch_trash"
        )
        
        // Samsung One UI deleted file patterns
        val samsungPatterns = listOf(
            ".samsung_deleted",
            "_samsung_deleted",
            ".sec_trash",
            "_sec_trash",
            ".oneui_deleted"
        )
        
        // Meizu Flyme deleted file patterns
        val meizuPatterns = listOf(
            ".flyme_deleted",
            "_flyme_deleted",
            ".mz_trash",
            "_mz_trash"
        )
        
        // Realme UI deleted file patterns
        val realmePatterns = listOf(
            ".realme_deleted",
            "_realme_deleted",
            ".realme_trash",
            "_realme_trash"
        )
        
        // Generic deleted file patterns (various file managers)
        val genericPatterns = listOf(
            ".bak",
            ".backup",
            ".old",
            ".tmp",
            ".temp",
            "~",
            ".deleted",
            "_deleted",
            ".trash",
            "_trash",
            ".recycle",
            "_recycle"
        )
        
        // Timestamp patterns (some phones add timestamps when deleting files)
        val timestampPatterns = listOf(
            Regex("_\\d{10}$"),        // Unix timestamp suffix _1234567890
            Regex("_\\d{13}$"),        // Millisecond timestamp suffix _1234567890123
            Regex("\\.\\d{10}$"),      // Unix timestamp suffix .1234567890
            Regex("\\.\\d{13}$")       // Millisecond timestamp suffix .1234567890123
        )
        
        // Combine all suffix patterns
        val allSuffixPatterns = oppoPatterns + miuiPatterns + huaweiPatterns + 
                               vivoPatterns + samsungPatterns + meizuPatterns + 
                               realmePatterns + genericPatterns
        
        // Remove suffix patterns
        for (pattern in allSuffixPatterns) {
            if (cleanName.endsWith(pattern)) {
                cleanName = cleanName.removeSuffix(pattern)
                break
            }
        }
        
        // Remove timestamp patterns
        for (pattern in timestampPatterns) {
            if (pattern.containsMatchIn(cleanName)) {
                val match = pattern.find(cleanName)
                if (match != null) {
                    cleanName = cleanName.removeSuffix(match.value)
                    break
                }
            }
        }
        
        // Handle prefix patterns (some phones add identifiers to file names)
        val prefixPatterns = listOf(
            "deleted_",
            "trash_",
            "backup_",
            "temp_",
            "old_",
            "bak_"
        )
        
        for (pattern in prefixPatterns) {
            if (cleanName.startsWith(pattern)) {
                cleanName = cleanName.removePrefix(pattern)
                break
            }
        }
        
        return cleanName
    }
}