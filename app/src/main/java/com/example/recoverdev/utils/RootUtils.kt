package com.example.recoverdev.utils

import android.util.Log
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * Root权限工具类
 */
object RootUtils {
    
    private const val TAG = "RootUtils"
    
    /**
     * 检查设备是否已Root
     */
    fun isDeviceRooted(): Bo<PERSON>an {
        return checkRootMethod1() || checkRootMethod2() || checkRootMethod3()
    }
    
    /**
     * 方法1：检查su命令
     */
    private fun checkRootMethod1(): <PERSON><PERSON>an {
        return try {
            val process = Runtime.getRuntime().exec("which su")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val result = reader.readLine()
            process.waitFor()
            !result.isNullOrEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 方法2：检查常见Root文件
     */
    private fun checkRootMethod2(): Bo<PERSON>an {
        val rootPaths = arrayOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su"
        )
        
        return rootPaths.any { path ->
            try {
                java.io.File(path).exists()
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * 方法3：尝试执行su命令
     */
    private fun checkRootMethod3(): Boolean {
        return try {
            val process = Runtime.getRuntime().exec("su -c 'id'")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val result = reader.readLine()
            process.waitFor()
            result?.contains("uid=0") == true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 执行Root命令
     */
    fun executeRootCommand(command: String): String? {
        return try {
            val process = Runtime.getRuntime().exec("su -c '$command'")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val result = StringBuilder()
            
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                result.append(line).append("\n")
            }
            
            val exitCode = process.waitFor()
            if (exitCode == 0) {
                result.toString().trim()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行Root命令失败: $command", e)
            null
        }
    }
    
    /**
     * 检查文件是否存在 (使用Root权限)
     */
    fun fileExistsWithRoot(filePath: String): Boolean {
        val result = executeRootCommand("test -f '$filePath' && echo 'exists'")
        return result == "exists"
    }
    
    /**
     * 读取文件内容 (使用Root权限)
     */
    fun readFileWithRoot(filePath: String): String? {
        return executeRootCommand("cat '$filePath'")
    }
    
    /**
     * 列出目录内容 (使用Root权限)
     */
    fun listDirectoryWithRoot(dirPath: String): List<String> {
        val result = executeRootCommand("ls -la '$dirPath'")
        return result?.split("\n")?.filter { it.isNotBlank() } ?: emptyList()
    }
}