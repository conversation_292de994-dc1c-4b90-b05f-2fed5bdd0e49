package com.example.recoverdev.utils

import android.content.Context
import android.content.res.Configuration
import java.util.*

object LanguageUtils {
    
    fun setLocale(context: Context, languageCode: String): Context {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        
        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)
        
        return context.createConfigurationContext(config)
    }
    
    fun getLanguageCode(language: String): String {
        return when (language) {
            "English" -> "en"
            "Deutsch" -> "de"
            "Русский язык" -> "ru"
            else -> "en"
        }
    }
    
    fun getLanguageName(languageCode: String): String {
        return when (languageCode) {
            "en" -> "English"
            "de" -> "Deutsch"
            "ru" -> "Русский язык"
            else -> "English"
        }
    }
}