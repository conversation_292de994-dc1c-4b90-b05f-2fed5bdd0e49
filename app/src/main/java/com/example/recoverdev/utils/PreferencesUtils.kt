package com.example.recoverdev.utils

import android.content.Context
import android.content.SharedPreferences

object PreferencesUtils {
    
    private const val PREFS_NAME = "RecoverDevPrefs"
    private const val KEY_NOTIFICATION_PERMISSION_REQUESTED = "notification_permission_requested"
    private const val KEY_FIRST_LAUNCH = "first_launch"
    
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * Check if this is the first launch of the app
     */
    fun isFirstLaunch(context: Context): Boolean {
        return getPreferences(context).getBoolean(KEY_FIRST_LAUNCH, true)
    }
    
    /**
     * Mark that the app has been launched before
     */
    fun setFirstLaunchCompleted(context: Context) {
        getPreferences(context).edit()
            .putBoolean(KEY_FIRST_LAUNCH, false)
            .apply()
    }
    
    /**
     * Check if notification permission has been requested before
     */
    fun hasNotificationPermissionBeenRequested(context: Context): <PERSON><PERSON>an {
        return getPreferences(context).getBoolean(KEY_NOTIFICATION_PERMISSION_REQUESTED, false)
    }
    
    /**
     * Mark that notification permission has been requested
     */
    fun setNotificationPermissionRequested(context: Context) {
        getPreferences(context).edit()
            .putBoolean(KEY_NOTIFICATION_PERMISSION_REQUESTED, true)
            .apply()
    }
}