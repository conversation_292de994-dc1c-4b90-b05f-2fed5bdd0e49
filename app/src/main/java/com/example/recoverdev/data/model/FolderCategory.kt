package com.example.recoverdev.data.model

data class FolderCategory(
    val id: String,
    val name: String,
    val path: String,
    val fileCount: Int,
    val files: List<RecoverableFile>,
    val isSpecialFolder: Boolean = false // 标识是否为特殊文件夹（如缩略图等）
)

data class ScanResult(
    val fileType: FileType,
    val totalFiles: Int,
    val allFiles: List<RecoverableFile>,
    val folderCategories: List<FolderCategory>
)