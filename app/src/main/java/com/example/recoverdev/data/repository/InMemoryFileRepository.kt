package com.example.recoverdev.data.repository

import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.data.model.ScanResult
import com.example.recoverdev.recovery.FileRecoveryEngine
import com.example.recoverdev.utils.FolderCategoryUtils
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class InMemoryFileRepository(
    private val recoveryEngine: FileRecoveryEngine,
    private val context: android.content.Context? = null
) {
    
    private var onScanPathUpdate: ((String) -> Unit)? = null
    
    fun setOnScanPathUpdate(callback: (String) -> Unit) {
        onScanPathUpdate = callback
    }
    
    private val _recoverableFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    private val _recoveredFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    
    // Recovery directory path
    private val recoveryDirectory = "/storage/emulated/0/RecoveredFiles"
    
    // Create separate StateFlow for each file type
    private val _recoverablePhotoFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    private val _recoverableVideoFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    private val _recoverableAudioFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    private val _recoverableOtherFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())

    fun clearScanCache() {
        _recoverableFiles.value = emptyList()
        _recoverablePhotoFiles.value = emptyList()
        _recoverableVideoFiles.value = emptyList()
        _recoverableAudioFiles.value = emptyList()
        _recoverableOtherFiles.value = emptyList()
    }
    
    fun getRecoverableFilesByType(type: FileType): Flow<List<RecoverableFile>> {
        return when (type) {
            FileType.PHOTO -> _recoverablePhotoFiles.asStateFlow()
            FileType.VIDEO -> _recoverableVideoFiles.asStateFlow()
            FileType.AUDIO -> _recoverableAudioFiles.asStateFlow()
            FileType.OTHER -> _recoverableOtherFiles.asStateFlow()
        }
    }
    
    fun getRecoveredFiles(): Flow<List<RecoverableFile>> {
        return _recoveredFiles.asStateFlow()
    }
    
    suspend fun scanForFilesWithCategories(type: FileType): ScanResult {
        try {
            // Create a new recovery engine with path update callback for this scan
            val scanEngine = FileRecoveryEngine(
                context = context,
                onScanPathUpdate = onScanPathUpdate
            )
            
            val scannedFiles = scanEngine.scanDeletedFiles(type)
            _recoverableFiles.value = scannedFiles
            
            // Update corresponding StateFlow for the file type
            updateFilesByType(type, scannedFiles)
            
            // Create scan results categorized by folder
            return FolderCategoryUtils.createScanResult(scannedFiles, type)
        } catch (e: Exception) {
            throw Exception("Files scan failed: ${e.message}")
        }
    }
    
    suspend fun recoverFiles(files: List<RecoverableFile>): List<RecoverableFile> {
        val recoveredFiles = files.map { file ->
            val recoveredPath = recoveryEngine.recoverFile(file)
            file.copy(
                isRecovered = true,
                recoveredPath = recoveredPath
            )
        }
        
        // Immediately remove recovered files from the corresponding type list
        files.forEach { file ->
            removeFileFromTypeList(file)
        }
        
        _recoveredFiles.value = _recoveredFiles.value + recoveredFiles
        
        return recoveredFiles
    }
    
    suspend fun deleteFiles(files: List<RecoverableFile>) {
        // Immediately remove deleted files from the corresponding type list
        files.forEach { file ->
            removeFileFromTypeList(file)
        }
        
        _recoverableFiles.value = _recoverableFiles.value.filter { file ->
            !files.any { it.id == file.id }
        }
    }
    
    suspend fun deleteFilesPermanently(files: List<RecoverableFile>) {
        files.forEach { file ->
            recoveryEngine.deleteFilePermanently(file)
        }
        deleteFiles(files)
    }
    
    /**
     * Updates the file list for a specific type
     */
    private fun updateFilesByType(type: FileType, files: List<RecoverableFile>) {
        val filteredFiles = files.filter { it.type == type && !it.isRecovered }
        when (type) {
            FileType.PHOTO -> _recoverablePhotoFiles.value = filteredFiles
            FileType.VIDEO -> _recoverableVideoFiles.value = filteredFiles
            FileType.AUDIO -> _recoverableAudioFiles.value = filteredFiles
            FileType.OTHER -> _recoverableOtherFiles.value = filteredFiles
        }
    }
    
    /**
     * Recovers a single file
     */
    suspend fun recoverFile(file: RecoverableFile): RecoverableFile {
        val recoveredPath = recoveryEngine.recoverFile(file)
        val recoveredFile = file.copy(
            isRecovered = true,
            recoveredPath = recoveredPath
        )
        
        // Remove from recoverable list
        removeFileFromTypeList(file)
        _recoverableFiles.value = _recoverableFiles.value.filter { it.id != file.id }
        
        // Add to recovered list
        _recoveredFiles.value = _recoveredFiles.value + recoveredFile
        
        return recoveredFile
    }
    
    /**
     * Permanently deletes a single file
     */
    suspend fun deleteFile(file: RecoverableFile) {
        // If it's a recovered file, delete from the recovered list
        if (file.isRecovered) {
            _recoveredFiles.value = _recoveredFiles.value.filter { it.id != file.id }
            // Delete the actual recovered file
            file.recoveredPath?.let { path ->
                try {
                    val fileToDelete = java.io.File(path)
                    if (fileToDelete.exists()) {
                        fileToDelete.delete()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            // If it's a recoverable file, delete from the recoverable list
            removeFileFromTypeList(file)
            _recoverableFiles.value = _recoverableFiles.value.filter { it.id != file.id }
        }
        
        // Permanently delete the original file
        recoveryEngine.deleteFilePermanently(file)
    }

    /**
     * Removes a file from the corresponding type list
     */
    private fun removeFileFromTypeList(file: RecoverableFile) {
        when (file.type) {
            FileType.PHOTO -> {
                _recoverablePhotoFiles.value = _recoverablePhotoFiles.value.filter { it.id != file.id }
            }
            FileType.VIDEO -> {
                _recoverableVideoFiles.value = _recoverableVideoFiles.value.filter { it.id != file.id }
            }
            FileType.AUDIO -> {
                _recoverableAudioFiles.value = _recoverableAudioFiles.value.filter { it.id != file.id }
            }
            FileType.OTHER -> {
                _recoverableOtherFiles.value = _recoverableOtherFiles.value.filter { it.id != file.id }
            }
        }
    }
    
    /**
     * Scans the recovery directory and rebuilds the recovered file list
     * Called at app startup to ensure recovered files are persistently displayed
     */
    suspend fun loadRecoveredFilesFromDirectory() {
        try {
            val recoveryDir = java.io.File(recoveryDirectory)
            if (!recoveryDir.exists() || !recoveryDir.canRead()) {
                // If recovery directory does not exist, clear the recovered file list
                _recoveredFiles.value = emptyList()
                return
            }
            
            val recoveredFiles = mutableListOf<RecoverableFile>()
            
            // Recursively scan all files in the recovery directory
            scanRecoveryDirectory(recoveryDir, recoveredFiles)
            
            // Update the recovered file list
            _recoveredFiles.value = recoveredFiles
            
            android.util.Log.d("InMemoryFileRepository", "Loaded ${recoveredFiles.size} recovered files from recovery directory")
            
        } catch (e: Exception) {
            android.util.Log.e("InMemoryFileRepository", "Failed to load recovered files", e)
            // On error, clear the list to avoid displaying invalid files
            _recoveredFiles.value = emptyList()
        }
    }
    
    /**
     * Cleans up non-existent files in the recovered file list
     * Can be called when the user enters the recovered data page
     */
    suspend fun cleanupNonExistentRecoveredFiles() {
        try {
            val currentFiles = _recoveredFiles.value
            val existingFiles = currentFiles.filter { file ->
                file.recoveredPath?.let { path ->
                    val fileObj = java.io.File(path)
                    val exists = fileObj.exists() && fileObj.canRead()
                    if (!exists) {
                        android.util.Log.d("InMemoryFileRepository", "Removing non-existent file: ${file.name} (path: $path)")
                    }
                    exists
                } ?: false
            }
            
            if (existingFiles.size != currentFiles.size) {
                _recoveredFiles.value = existingFiles
                android.util.Log.d("InMemoryFileRepository", "Cleanup complete: Removed ${currentFiles.size - existingFiles.size} non-existent files")
            }
            
        } catch (e: Exception) {
            android.util.Log.e("InMemoryFileRepository", "Failed to clean up recovered files", e)
        }
    }
    
    /**
     * Recursively scans the recovery directory
     */
    private fun scanRecoveryDirectory(dir: java.io.File, recoveredFiles: MutableList<RecoverableFile>) {
        try {
            dir.listFiles()?.forEach { file ->
                when {
                    file.isFile && file.exists() && file.canRead() -> {
                        // Only add to list if file exists and is readable
                        val recoveredFile = createRecoveredFileFromPath(file)
                        recoveredFiles.add(recoveredFile)
                    }
                    file.isDirectory && file.exists() && file.canRead() -> {
                        // Recursively scan subdirectories
                        scanRecoveryDirectory(file, recoveredFiles)
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.w("InMemoryFileRepository", "Failed to scan directory: ${dir.path}", e)
        }
    }
    
    /**
     * Creates a recovered file object from a file path
     */
    private fun createRecoveredFileFromPath(file: java.io.File): RecoverableFile {
        val extension = file.extension.lowercase()
        val fileType = determineFileType(extension)
        
        return RecoverableFile(
            id = java.util.UUID.randomUUID().toString(),
            name = file.name,
            path = "", // Original path is no longer important
            size = file.length(),
            dateModified = file.lastModified(),
            type = fileType,
            format = extension.uppercase(),
            mimeType = getMimeType(extension),
            isRecovered = true,
            recoveredPath = file.absolutePath,
            thumbnailPath = if (fileType == FileType.PHOTO || fileType == FileType.VIDEO) file.absolutePath else null
        )
    }
    
    /**
     * Determines file type based on file extension
     */
    private fun determineFileType(extension: String): FileType {
        return when (extension) {
            in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "heic", "raw", "tiff", "svg") -> FileType.PHOTO
            in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp", "m4v", "mpg", "mpeg", "rm", "rmvb") -> FileType.VIDEO
            in listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus", "amr", "mid", "midi") -> FileType.AUDIO
            else -> FileType.OTHER
        }
    }
    
    /**
     * Gets MIME type based on file extension
     */
    private fun getMimeType(extension: String): String? {
        return when (extension) {
            // Images
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            "heic" -> "image/heic"
            "raw" -> "image/x-canon-cr2"
            "tiff" -> "image/tiff"
            "svg" -> "image/svg+xml"
            // Videos
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "wmv" -> "video/x-ms-wmv"
            "flv" -> "video/x-flv"
            "webm" -> "video/webm"
            "3gp" -> "video/3gpp"
            "m4v" -> "video/x-m4v"
            "mpg", "mpeg" -> "video/mpeg"
            "rm" -> "application/vnd.rn-realmedia"
            "rmvb" -> "application/vnd.rn-realmedia-vbr"
            // Audio
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            "aac" -> "audio/aac"
            "ogg" -> "audio/ogg"
            "wma" -> "audio/x-ms-wma"
            "m4a" -> "audio/mp4"
            "opus" -> "audio/opus"
            "amr" -> "audio/amr"
            "mid", "midi" -> "audio/midi"
            // Others
            "pdf" -> "application/pdf"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "txt" -> "text/plain"
            "zip" -> "application/zip"
            "apk" -> "application/vnd.android.package-archive"
            else -> null
        }
    }
}