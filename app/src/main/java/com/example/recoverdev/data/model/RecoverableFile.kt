package com.example.recoverdev.data.model

data class RecoverableFile(
    val id: String,
    val name: String,
    val path: String,
    val size: Long,
    val dateModified: Long,
    val type: FileType,
    val format: String,
    val mimeType: String? = null,
    val isRecovered: Boolean = false,
    val recoveredPath: String? = null,
    val thumbnailPath: String? = null,
    // 图片特有属性
    val width: Int? = null,
    val height: Int? = null,
    val resolution: String? = null,
    // 视频/音频特有属性
    val duration: Long? = null,
    val dimension: String? = null
)

enum class FileType {
    PHOTO, VIDEO, AUDIO, OTHER
}