package com.example.recoverdev.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.data.repository.InMemoryFileRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class RecoveryViewModel(
    private val repository: InMemoryFileRepository
) : ViewModel() {
    
    private val _files = MutableStateFlow<List<RecoverableFile>>(emptyList())
    val files: StateFlow<List<RecoverableFile>> = _files.asStateFlow()
    
    private val _folderName = MutableStateFlow<String?>(null)
    val folderName: StateFlow<String?> = _folderName.asStateFlow()
    
    private val _selectedFiles = MutableStateFlow<Set<String>>(emptySet())
    val selectedFiles: StateFlow<Set<String>> = _selectedFiles.asStateFlow()
    
    private val _isSelectionMode = MutableStateFlow(false)
    val isSelectionMode: StateFlow<Boolean> = _isSelectionMode.asStateFlow()
    
    private val _isRecovering = MutableStateFlow(false)
    val isRecovering: StateFlow<Boolean> = _isRecovering.asStateFlow()
    
    // Date group selection state
    private val _selectedDateGroups = MutableStateFlow<Set<String>>(emptySet())
    val selectedDateGroups: StateFlow<Set<String>> = _selectedDateGroups.asStateFlow()
    
    // Delete confirmation dialog state
    private val _showDeleteDialog = MutableStateFlow(false)
    val showDeleteDialog: StateFlow<Boolean> = _showDeleteDialog.asStateFlow()
    
    // No selection dialog state
    private val _showNoSelectionDialog = MutableStateFlow(false)
    val showNoSelectionDialog: StateFlow<Boolean> = _showNoSelectionDialog.asStateFlow()
    
    fun loadFiles(fileType: FileType) {
        viewModelScope.launch {
            repository.getRecoverableFilesByType(fileType).collect { fileList ->
                _files.value = fileList
                // Ensure selected state is synchronized with current file list
                val currentFileIds = fileList.map { it.id }.toSet()
                _selectedFiles.value = _selectedFiles.value.intersect(currentFileIds)
            }
        }
    }
    
    fun setFiles(files: List<RecoverableFile>) {
        _files.value = files
    }
    
    fun setFilesWithFolder(files: List<RecoverableFile>, folderName: String) {
        _files.value = files
        _folderName.value = folderName
    }
    
    fun clearFolderName() {
        _folderName.value = null
    }
    
    fun toggleFileSelection(fileId: String) {
        val currentSelection = _selectedFiles.value.toMutableSet()
        if (currentSelection.contains(fileId)) {
            currentSelection.remove(fileId)
        } else {
            currentSelection.add(fileId)
        }
        _selectedFiles.value = currentSelection
        
        // Update date group selection state
        updateDateGroupSelections()
        
        if (currentSelection.isEmpty()) {
//            _isSelectionMode.value = false
            _selectedDateGroups.value = emptySet()
        }
    }
    
    fun enterSelectionMode(fileId: String) {
        _isSelectionMode.value = true
        _selectedFiles.value = setOf(fileId)
        updateDateGroupSelections()
    }
    
    fun exitSelectionMode() {
        _isSelectionMode.value = false
        _selectedFiles.value = emptySet()
        _selectedDateGroups.value = emptySet()
    }
    
    fun toggleSelectionMode() {
        if (_isSelectionMode.value) {
            exitSelectionMode()
        } else {
            _isSelectionMode.value = true
        }
    }
    
    fun selectAllFiles() {
        val allFileIds = _files.value.map { it.id }.toSet()
        if (_selectedFiles.value.containsAll(allFileIds) && _selectedFiles.value.size == allFileIds.size) {
            // If all files are already selected, deselect all
            _selectedFiles.value = emptySet()
            _selectedDateGroups.value = emptySet()
        } else {
            // Otherwise, select all files
            _selectedFiles.value = allFileIds
            updateDateGroupSelections()
        }
    }
    
    fun isAllFilesSelected(): Boolean {
        val allFileIds = _files.value.map { it.id }.toSet()
        return _selectedFiles.value.containsAll(allFileIds) && _selectedFiles.value.size == allFileIds.size
    }
    
    fun recoverSelectedFiles() {
        viewModelScope.launch {
            _isRecovering.value = true
            try {
                val filesToRecover = _files.value.filter { it.id in _selectedFiles.value }
                repository.recoverFiles(filesToRecover)
                
                // Immediately remove recovered files from the current display list
                _files.value = _files.value.filter { it.id !in _selectedFiles.value }
                
                exitSelectionMode()
            } catch (e: Exception) {
                // Handle error
            } finally {
                _isRecovering.value = false
            }
        }
    }
    
    fun deleteSelectedFilesPermanently() {
        viewModelScope.launch {
            try {
                val filesToDelete = _files.value.filter { it.id in _selectedFiles.value }
                repository.deleteFilesPermanently(filesToDelete)
                
                // Immediately remove deleted files from the current display list
                _files.value = _files.value.filter { it.id !in _selectedFiles.value }
                
                exitSelectionMode()
                _showDeleteDialog.value = false // Hide delete dialog after deletion
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun showDeleteConfirmationDialog() {
        _showDeleteDialog.value = true
    }
    
    fun hideDeleteConfirmationDialog() {
        _showDeleteDialog.value = false
    }
    
    fun showNoSelectionDialog() {
        _showNoSelectionDialog.value = true
    }
    
    fun hideNoSelectionDialog() {
        _showNoSelectionDialog.value = false
    }
    
    fun recoverSingleFile(file: RecoverableFile) {
        viewModelScope.launch {
            try {
                repository.recoverFile(file)
                // Remove recovered file from the current display list
                _files.value = _files.value.filter { it.id != file.id }
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun deleteSingleFile(file: RecoverableFile) {
        viewModelScope.launch {
            try {
                repository.deleteFile(file)
                // Remove deleted file from the current display list
                _files.value = _files.value.filter { it.id != file.id }
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    // Date group selection related methods
    fun toggleDateGroupSelection(date: String) {
        val filesInDate = getFilesForDate(date)
        val currentSelected = _selectedFiles.value.toMutableSet()
        val currentDateGroups = _selectedDateGroups.value.toMutableSet()
        
        if (currentDateGroups.contains(date)) {
            // Deselect all files for this date
            filesInDate.forEach { file ->
                currentSelected.remove(file.id)
            }
            currentDateGroups.remove(date)
        } else {
            // Select all files for this date
            filesInDate.forEach { file ->
                currentSelected.add(file.id)
            }
            currentDateGroups.add(date)
            // Enter selection mode
            _isSelectionMode.value = true
        }
        
        _selectedFiles.value = currentSelected
        _selectedDateGroups.value = currentDateGroups
        
        // If no files are selected, exit selection mode
//        if (currentSelected.isEmpty()) {
//            _isSelectionMode.value = false
//        }
    }
    
    fun isDateGroupSelected(date: String): Boolean {
        return _selectedDateGroups.value.contains(date)
    }
    
    private fun updateDateGroupSelections() {
        val groupedFiles = _files.value.groupBy { file ->
            val date = Date(file.dateModified)
            SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date)
        }
        
        val newDateGroups = mutableSetOf<String>()
        groupedFiles.forEach { (date, filesInDate) ->
            val allFilesSelected = filesInDate.all { file ->
                _selectedFiles.value.contains(file.id)
            }
            if (allFilesSelected && filesInDate.isNotEmpty()) {
                newDateGroups.add(date)
            }
        }
        _selectedDateGroups.value = newDateGroups
    }
    
    private fun getFilesForDate(date: String): List<RecoverableFile> {
        return _files.value.filter { file ->
            val fileDate = Date(file.dateModified)
            val formattedDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(fileDate)
            formattedDate == date
        }
    }
}

