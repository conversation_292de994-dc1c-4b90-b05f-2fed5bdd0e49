package com.example.recoverdev.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.FolderCategory
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.data.repository.InMemoryFileRepository
import com.example.recoverdev.utils.FolderCategoryUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class RecoveredDataViewModel(
    private val repository: InMemoryFileRepository
) : ViewModel() {
    
    private val _selectedTab = MutableStateFlow(FileType.PHOTO)
    val selectedTab: StateFlow<FileType> = _selectedTab.asStateFlow()
    
    private val _recoveredFiles = MutableStateFlow<List<RecoverableFile>>(emptyList())
    val recoveredFiles: StateFlow<List<RecoverableFile>> = _recoveredFiles.asStateFlow()
    
    // Selection mode related states
    private val _isSelectionMode = MutableStateFlow(false)
    val isSelectionMode: StateFlow<Boolean> = _isSelectionMode.asStateFlow()
    
    private val _selectedFiles = MutableStateFlow<Set<String>>(emptySet())
    val selectedFiles: StateFlow<Set<String>> = _selectedFiles.asStateFlow()
    
    private val _selectedDateGroups = MutableStateFlow<Set<String>>(emptySet())
    val selectedDateGroups: StateFlow<Set<String>> = _selectedDateGroups.asStateFlow()
    
    private val _showDeleteDialog = MutableStateFlow(false)
    val showDeleteDialog: StateFlow<Boolean> = _showDeleteDialog.asStateFlow()
    
    init {
        // Load recovered files when app starts
        viewModelScope.launch {
            repository.loadRecoveredFilesFromDirectory()
            // Set up continuous monitoring of recovered files
            repository.getRecoveredFiles().collect { files ->
                _recoveredFiles.value = files
            }
        }
    }
    
    /**
     * Refresh recovered files list, clean up non-existent files
     * Can be called when user enters recovered data page
     */
    fun refreshRecoveredFiles() {
        viewModelScope.launch {
            repository.cleanupNonExistentRecoveredFiles()
        }
    }
    
    fun selectTab(fileType: FileType) {
        _selectedTab.value = fileType
        loadRecoveredFilesByType(fileType)
    }
    
    private fun loadRecoveredFilesByType(fileType: FileType) {
        // No need to load separately, filter directly from all recovered files
        // UI layer will automatically filter display based on selectedTab
    }
    
    fun getFilesByType(fileType: FileType): List<RecoverableFile> {
        return _recoveredFiles.value.filter { it.type == fileType }
    }
    
    /**
     * Get folder categories for OTHER file type
     */
    fun getFolderCategoriesForOtherFiles(): List<FolderCategory> {
        val otherFiles = getFilesByType(FileType.OTHER)
        return FolderCategoryUtils.groupFilesByFolder(otherFiles)
    }
    
    fun recoverFile(file: RecoverableFile) {
        viewModelScope.launch {
            repository.recoverFile(file)
        }
    }
    
    fun deleteFile(file: RecoverableFile) {
        viewModelScope.launch {
            repository.deleteFile(file)
            // The UI will automatically update through the flow collection in init
        }
    }
    
    // Selection mode related methods
    fun toggleSelectionMode() {
        _isSelectionMode.value = !_isSelectionMode.value
        if (!_isSelectionMode.value) {
            clearAllSelections()
        }
    }
    
    fun toggleFileSelection(fileId: String) {
        val currentSelected = _selectedFiles.value.toMutableSet()
        if (currentSelected.contains(fileId)) {
            currentSelected.remove(fileId)
        } else {
            currentSelected.add(fileId)
        }
        _selectedFiles.value = currentSelected
        updateDateGroupSelections()
    }
    
    fun toggleDateGroupSelection(date: String) {
        val filesInDate = getFilesForDate(date)
        val currentSelected = _selectedFiles.value.toMutableSet()
        val currentDateGroups = _selectedDateGroups.value.toMutableSet()
        
        if (currentDateGroups.contains(date)) {
            // Deselect all files for this date
            filesInDate.forEach { file ->
                currentSelected.remove(file.id)
            }
            currentDateGroups.remove(date)
        } else {
            // Select all files for this date
            filesInDate.forEach { file ->
                currentSelected.add(file.id)
            }
            currentDateGroups.add(date)
        }
        
        _selectedFiles.value = currentSelected
        _selectedDateGroups.value = currentDateGroups
    }
    
    fun selectAllFiles() {
        val currentTabFiles = getFilesByType(_selectedTab.value)
        val allFileIds = currentTabFiles.map { it.id }.toSet()
        _selectedFiles.value = allFileIds
        
        // Update date group selection status
        val allDates = currentTabFiles.groupBy { file ->
            val date = Date(file.dateModified)
            SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date)
        }.keys
        _selectedDateGroups.value = allDates
    }
    
    fun clearAllSelections() {
        _selectedFiles.value = emptySet()
        _selectedDateGroups.value = emptySet()
    }
    
    fun isFileSelected(fileId: String): Boolean {
        return _selectedFiles.value.contains(fileId)
    }
    
    fun isDateGroupSelected(date: String): Boolean {
        return _selectedDateGroups.value.contains(date)
    }
    
    fun isAllSelected(): Boolean {
        val currentTabFiles = getFilesByType(_selectedTab.value)
        return currentTabFiles.isNotEmpty() && 
               _selectedFiles.value.size == currentTabFiles.size
    }
    
    private fun updateDateGroupSelections() {
        val currentTabFiles = getFilesByType(_selectedTab.value)
        val groupedFiles = currentTabFiles.groupBy { file ->
            val date = Date(file.dateModified)
            SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date)
        }
        
        val newDateGroups = mutableSetOf<String>()
        groupedFiles.forEach { (date, filesInDate) ->
            val allFilesSelected = filesInDate.all { file ->
                _selectedFiles.value.contains(file.id)
            }
            if (allFilesSelected && filesInDate.isNotEmpty()) {
                newDateGroups.add(date)
            }
        }
        _selectedDateGroups.value = newDateGroups
    }
    
    private fun getFilesForDate(date: String): List<RecoverableFile> {
        val currentTabFiles = getFilesByType(_selectedTab.value)
        return currentTabFiles.filter { file ->
            val fileDate = Date(file.dateModified)
            val formattedDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(fileDate)
            formattedDate == date
        }
    }
    
    fun showDeleteConfirmationDialog() {
        _showDeleteDialog.value = true
    }
    
    fun hideDeleteConfirmationDialog() {
        _showDeleteDialog.value = false
    }
}
