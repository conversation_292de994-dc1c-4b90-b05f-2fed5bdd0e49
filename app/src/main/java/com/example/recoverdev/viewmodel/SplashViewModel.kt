package com.example.recoverdev.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.recoverdev.utils.PreferenceUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Splash screen state
 */
sealed class SplashState {
    object Initial : SplashState()
    object ShowingTermsButton : SplashState()
    data class Loading(val progress: Float) : SplashState()
    object NavigateToGuide : SplashState()
    object NavigateToMain : SplashState()
}

/**
 * Splash screen ViewModel
 * Manages terms agreement state and splash screen logic
 */
class SplashViewModel : ViewModel() {
    
    private val _state = MutableStateFlow<SplashState>(SplashState.Initial)
    val state: StateFlow<SplashState> = _state.asStateFlow()
    
    init {
        checkTermsAgreement()
    }
    
    /**
     * Check if terms have been agreed to
     */
    private fun checkTermsAgreement() {
        val hasAgreed = PreferenceUtils.hasAgreedToTerms()
        if (hasAgreed) {
            // Terms already agreed, start loading directly (4 seconds)
            startLoading(4000L)
        } else {
            // Terms not agreed, show Start Now button
            _state.value = SplashState.ShowingTermsButton
        }
    }
    
    /**
     * User clicks Start Now button
     */
    fun onStartNowClicked() {
        // Save terms agreement state
        PreferenceUtils.setTermsAgreed()
        
        // Start loading (8 seconds for first time)
        startLoading(8000L)
    }
    
    /**
     * Start loading process
     */
    private fun startLoading(duration: Long) {
        viewModelScope.launch {
            val updateInterval = 16L // About 60fps, smoother update frequency
            val totalSteps = duration / updateInterval
            
            for (step in 0..totalSteps) {
                // Use easing function to make progress more natural
                val linearProgress = step.toFloat() / totalSteps.toFloat()
                val easedProgress = easeOutCubic(linearProgress)
                _state.value = SplashState.Loading(easedProgress)
                
                if (step < totalSteps) {
                    delay(updateInterval)
                }
            }
            
            // Ensure final progress is 100%
            _state.value = SplashState.Loading(1.0f)
            delay(100) // Brief pause to let user see 100%
            
            // Loading complete, check if guide page needs to be shown
            if (PreferenceUtils.isGuideShown()) {
                _state.value = SplashState.NavigateToMain
            } else {
                _state.value = SplashState.NavigateToGuide
            }
        }
    }
    
    /**
     * Easing function: ease-out cubic (more natural animation effect)
     * Starts fast, then gradually slows down, finally reaches target smoothly
     */
    private fun easeOutCubic(t: Float): Float {
        if (t >= 1f) return 1f
        if (t <= 0f) return 0f
        
        val t1 = t - 1f
        return t1 * t1 * t1 + 1f
    }
    
    /**
     * Reset state (for after navigation completion)
     */
    fun resetNavigationState() {
        if (_state.value is SplashState.NavigateToMain || _state.value is SplashState.NavigateToGuide) {
            _state.value = SplashState.Initial
        }
    }
    
    /**
     * Check if terms have been agreed to (for external use)
     */
    fun hasAgreedToTerms(): Boolean {
        return PreferenceUtils.hasAgreedToTerms()
    }
    
    /**
     * Clear terms agreement state (for testing use)
     */
    fun clearTermsAgreement() {
        PreferenceUtils.clearTermsAgreement()
    }
}