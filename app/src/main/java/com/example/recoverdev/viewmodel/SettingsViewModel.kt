package com.example.recoverdev.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.example.recoverdev.utils.LanguageUtils
import com.example.recoverdev.utils.PreferenceUtils

class SettingsViewModel(private val context: Context) : ViewModel() {
    
    private val _currentLanguage = MutableStateFlow(PreferenceUtils.getCurrentLanguage())
    val currentLanguage: StateFlow<String> = _currentLanguage.asStateFlow()
    
    fun setLanguage(language: String) {
        PreferenceUtils.setLanguage(language)
        _currentLanguage.value = language
    }
    
    fun getLanguageCode(language: String): String {
        return LanguageUtils.getLanguageCode(language)
    }
    
    fun applyLanguage(language: String) {
        val languageCode = getLanguageCode(language)
        LanguageUtils.setLocale(context, languageCode)
        setLanguage(language)
    }
}