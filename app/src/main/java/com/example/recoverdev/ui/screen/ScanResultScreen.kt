package com.example.recoverdev.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType
@Composable
fun ScanResultScreenContent(
    modifier: Modifier = Modifier,
    fileType: FileType,
    foundCount: Int,
    onViewFiles: () -> Unit
) {
    // Get file type text for this composable
    val fileTypeText = getFileTypeText(fileType)
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (foundCount > 0) {
            Spacer(modifier = Modifier.height(60.dp))

            Image(
                painter = painterResource(id = R.mipmap.img_complete),
                contentDescription = "Scan completed",
                modifier = Modifier.size(160.dp)
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Text(
                text = stringResource(R.string.scan_completed),
                fontSize = 24.sp,
                fontWeight = FontWeight.SemiBold,
                color = colorResource(R.color.first_text_black),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(color = colorResource(R.color.btn_orange),
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 21.sp)) {
                        append("$foundCount $fileTypeText")
                    }
                    withStyle(style = SpanStyle(color = colorResource(R.color.first_text_black),
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 14.sp)) {
                        append(" " + stringResource(R.string.scan_found_recovery))
                    }
                },
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onViewFiles,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .padding(horizontal = 40.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = colorResource(R.color.btn_orange)
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Text(
                    text = stringResource(R.string.view),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White
                )
            }
        } else {
            val typeRes = when (fileType) {
                FileType.PHOTO -> R.mipmap.no_image
                FileType.VIDEO -> R.mipmap.no_video
                FileType.AUDIO -> R.mipmap.no_audio
                FileType.OTHER -> R.mipmap.no_file
            }

            Image(
                painter = painterResource(id = typeRes),
                contentDescription = "No files found",
                modifier = Modifier.size(160.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = stringResource(
                    R.string.no_files_to_recover,
                    fileTypeText.lowercase()
                ),
                fontSize = 16.sp,
                color = Color.Black,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun getFileTypeText(fileType: FileType): String {
    return when (fileType) {
        FileType.PHOTO -> stringResource(R.string.photos_text)
        FileType.VIDEO -> stringResource(R.string.videos_text)
        FileType.AUDIO -> stringResource(R.string.audio_files_text)
        FileType.OTHER -> stringResource(R.string.files_text)
    }
}