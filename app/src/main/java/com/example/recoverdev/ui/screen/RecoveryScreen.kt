package com.example.recoverdev.ui.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.ui.components.FileItem
import com.example.recoverdev.ui.components.GridFileItem
import com.example.recoverdev.ui.components.DeleteConfirmationDialog
import com.example.recoverdev.ui.components.NoSelectionDialog
import com.example.recoverdev.viewmodel.RecoveryViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecoveryScreen(
    fileType: FileType,
    files: List<RecoverableFile>,
    onBackClick: () -> Unit,
    onFileClick: (RecoverableFile) -> Unit,
    onRecoverClick: () -> Unit,
    viewModel: RecoveryViewModel
) {
    val selectedFiles by viewModel.selectedFiles.collectAsState()
    val isSelectionMode by viewModel.isSelectionMode.collectAsState()
    val isRecovering by viewModel.isRecovering.collectAsState()

    // Use the files state from ViewModel instead of the passed parameter
    val currentFiles by viewModel.files.collectAsState()
    
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showNoSelectionDialog by remember { mutableStateOf(false) }
    
    LaunchedEffect(files) {
        viewModel.setFiles(files)
    }
    
    LaunchedEffect(Unit) {
        viewModel.exitSelectionMode()
    }
    
    BackHandler {
        if (isSelectionMode) {
            viewModel.exitSelectionMode()
        } else {
            onBackClick()
        }
    }
    
    DeleteConfirmationDialog(
        showDialog = showDeleteDialog,
        onDismiss = { showDeleteDialog = false },
        onConfirm = { viewModel.deleteSelectedFilesPermanently() }
    )
    
    NoSelectionDialog(
        showDialog = showNoSelectionDialog,
        onDismiss = { showNoSelectionDialog = false }
    )
    
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        if (currentFiles.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(R.string.no_recoverable_files),
                    fontSize = 16.sp,
                    color = colorResource(R.color.first_text_gray)
                )
            }
        } else {
            // Sort files by date (newest first) and group by date
            val sortedFiles = currentFiles.sortedByDescending { it.dateModified }
            val groupedFiles = sortedFiles.groupBy { file ->
                val date = Date(file.dateModified)
                SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date)
            }
            
            LazyColumn(
                modifier = Modifier.weight(1f),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Display files grouped by date
                groupedFiles.forEach { (date, filesInDate) ->
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = date,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.Black
                            )
                            
                            if (isSelectionMode) {
                                Box(
                                    modifier = Modifier
                                        .padding(2.dp)
                                        .clickable { viewModel.toggleDateGroupSelection(date) },
                                    ) {
                                    if (viewModel.isDateGroupSelected(date)) {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = "Selected",
                                            tint = Color.White,
                                            modifier = Modifier
                                                .size(20.dp)
                                                .background(color = colorResource(R.color.btn_orange), RoundedCornerShape(4.dp))
                                        )
                                    } else {
                                        Box(
                                            modifier = Modifier
                                                .size(20.dp)
                                                .background(Color(0xFFFFE0C1), RoundedCornerShape(4.dp))
                                                .border(
                                                    width = 2.dp,
                                                    color = colorResource(R.color.btn_orange),
                                                    shape = RoundedCornerShape(4.dp)
                                                )
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    if (fileType == FileType.PHOTO || fileType == FileType.VIDEO || fileType == FileType.AUDIO) {
                        val chunkedFiles = filesInDate.chunked(4)
                        items(chunkedFiles) { rowFiles ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                rowFiles.forEach { file ->
                                    GridFileItem(
                                        file = file,
                                        isSelected = selectedFiles.contains(file.id),
                                        isSelectionMode = isSelectionMode,
                                        onClick = { 
                                            if (isSelectionMode) {
                                                viewModel.toggleFileSelection(file.id)
                                            } else {
                                                onFileClick(file)
                                            }
                                        },
                                        onLongClick = {
                                            if (!isSelectionMode) {
                                                viewModel.enterSelectionMode(file.id)
                                            }
                                        },
                                        modifier = Modifier.weight(1f)
                                    )
                                }
                                // Fill remaining spaces if row is not complete
                                repeat(4 - rowFiles.size) {
                                    Spacer(modifier = Modifier.weight(1f))
                                }
                            }
                        }
                    } else {
                        // List layout for other files
                        items(filesInDate) { file ->
                            FileItem(
                                file = file,
                                isSelected = selectedFiles.contains(file.id),
                                isSelectionMode = isSelectionMode,
                                onClick = { 
                                    if (isSelectionMode) {
                                        viewModel.toggleFileSelection(file.id)
                                    } else {
                                        onFileClick(file)
                                    }
                                },
                                onLongClick = {
                                    if (!isSelectionMode) {
                                        viewModel.enterSelectionMode(file.id)
                                    }
                                },
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
        }
        
        if (isSelectionMode && selectedFiles.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                contentAlignment = Alignment.Center
            ) {
                Button(
                    onClick = { 
                        if (selectedFiles.isEmpty()) {
                            showNoSelectionDialog = true
                        } else {
                            onRecoverClick()
                        }
                    },
                    enabled = !isRecovering,
                    shape = RoundedCornerShape(16.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(R.color.btn_orange)
                    ),
                    modifier = Modifier
                        .widthIn(min = 240.dp),
                ) {
                    if (isRecovering) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = Color.White
                        )
                    } else {
                        Text(
                            text = stringResource(R.string.recover_selected, selectedFiles.size),
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(8.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun RecoveryScreenContent(
    fileType: FileType,
    files: List<RecoverableFile>,
    onFileClick: (RecoverableFile) -> Unit,
    onRecoverClick: () -> Unit,
    onBackClick: () -> Unit,
    viewModel: RecoveryViewModel
) {
    val showDeleteDialog by viewModel.showDeleteDialog.collectAsState()
    
    val showNoSelectionDialog by viewModel.showNoSelectionDialog.collectAsState()
    
    DeleteConfirmationDialog(
        showDialog = showDeleteDialog,
        onDismiss = { viewModel.hideDeleteConfirmationDialog() },
        onConfirm = { viewModel.deleteSelectedFilesPermanently() }
    )
    
    NoSelectionDialog(
        showDialog = showNoSelectionDialog,
        onDismiss = { viewModel.hideNoSelectionDialog() }
    )
    
    RecoveryScreen(
        fileType = fileType,
        files = files,
        onBackClick = onBackClick,
        onFileClick = onFileClick,
        onRecoverClick = onRecoverClick,
        viewModel = viewModel
    )
}