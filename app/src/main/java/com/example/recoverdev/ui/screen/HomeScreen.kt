package com.example.recoverdev.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    onRecoveryClick: (FileType) -> Unit,
    onRecoveredDataClick: () -> Unit,
    onSettingsClick: () -> Unit,
    onInfoClick: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Top bar
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.file_recovery),
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
            
            IconButton(onClick = onSettingsClick) {
                Image(
                    painter = painterResource(id = R.mipmap.nav_set),
                    contentDescription = "Settings",
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        MainScreenContent(
            onRecoveryClick = onRecoveryClick,
            onRecoveredDataClick = onRecoveredDataClick,
            onInfoClick = onInfoClick
        )
    }
}

/**
 * Main screen content area, excluding TopBar
 */
@Composable
fun MainScreenContent(
    modifier: Modifier = Modifier,
    onRecoveryClick: (FileType) -> Unit,
    onRecoveredDataClick: () -> Unit,
    onInfoClick: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        PhotoRecoveryCard(
            onClick = { onRecoveryClick(FileType.PHOTO) }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            RecoveryOptionCard(
                title = stringResource(R.string.video),
                subtitle = stringResource(R.string.recover_video),
                iconRes = R.mipmap.home_video,
                modifier = Modifier
                    .weight(1f)
                    .height(120.dp),
                onClick = { onRecoveryClick(FileType.VIDEO) }
            )
            
            RecoveryOptionCard(
                title = stringResource(R.string.audio),
                subtitle = stringResource(R.string.recover_audio),
                iconRes = R.mipmap.home_audio,
                modifier = Modifier
                    .weight(1f)
                    .height(120.dp),
                onClick = { onRecoveryClick(FileType.AUDIO) }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            RecoveryOptionCard(
                title = stringResource(R.string.other_files),
                subtitle = stringResource(R.string.recover_other_files),
                iconRes = R.mipmap.home_other,
                modifier = Modifier
                    .weight(1f)
                    .height(120.dp),
                onClick = { onRecoveryClick(FileType.OTHER) }
            )
            
            RecoveryOptionCard(
                title = stringResource(R.string.data),
                subtitle = stringResource(R.string.recovered_data),
                iconRes = R.mipmap.home_data,
                modifier = Modifier
                    .weight(1f)
                    .height(120.dp),
                onClick = onRecoveredDataClick
            )
        }
    }
}

@Composable
fun PhotoRecoveryCard(
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Background with gradient-like effect
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Color.White,
                        RoundedCornerShape(16.dp)
                    )
            )
            
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Icon
                Image(
                    painter = painterResource(id = R.mipmap.home_image),
                    contentDescription = "Photo",
                    modifier = Modifier.size(68.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // Text content
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = stringResource(R.string.photo),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = colorResource(R.color.first_text_black)
                    )
                    
                    Text(
                        text = stringResource(R.string.recovery_photo),
                        fontSize = 12.sp,
                        color = colorResource(R.color.first_text_gray),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }

            }
        }
    }
}

@Composable
fun RecoveryOptionCard(
    title: String,
    subtitle: String,
    iconRes: Int,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Background decoration
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Color.White,
                        RoundedCornerShape(16.dp)
                    )
            )
            
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // First row: Icon and Title
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = iconRes),
                        contentDescription = title,
                        modifier = Modifier.size(46.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = title,
                        fontSize = 14.sp,
                        lineHeight = 15.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.first_text_black)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Second row: Subtitle
                Text(
                    text = subtitle,
                    fontSize = 12.sp,
                    lineHeight = 15.sp,
                    color = colorResource(R.color.first_text_gray),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}
