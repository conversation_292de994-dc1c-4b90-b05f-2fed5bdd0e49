package com.example.recoverdev.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import coil.request.videoFrameMillis
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.utils.FileUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

// Helper function to get file type icon based on file extension
private fun getFileTypeIcon(fileName: String): Int {
    // Clean the file name to remove manufacturer-specific suffixes for deleted files
    val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
    val extension = cleanFileName.substringAfterLast('.', "").lowercase()
    return when (extension) {
        "pdf" -> R.mipmap.type_pdf
        "doc", "docx" -> R.mipmap.type_doc
        "xls", "xlsx" -> R.mipmap.type_excel
        "ppt", "pptx" -> R.mipmap.type_ppt
        "txt" -> R.mipmap.type_txt
        "html", "htm" -> R.mipmap.type_html
        "mp3", "wav", "aac", "flac", "ogg" -> R.mipmap.type_mp3
        "mp4", "avi", "mkv", "mov", "wmv", "flv" -> R.mipmap.type_video
        "jpg", "jpeg", "png", "gif", "bmp", "webp" -> R.mipmap.type_image
        "zip", "rar", "7z", "tar", "gz" -> R.mipmap.type_zip
        "apk" -> R.mipmap.type_apk
        else -> R.mipmap.type_other
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun FileItem(
    file: RecoverableFile,
    isSelected: Boolean = false,
    isSelectionMode: Boolean = false,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = 80.dp)
            .background(Color.White, RoundedCornerShape(12.dp))
            .combinedClickable(
                onClick = onClick,
                onLongClick = onLongClick
            )
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = getFileTypeIcon(file.name)),
            contentDescription = "File type icon",
            modifier = Modifier.size(40.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        // File info column
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = file.name,
                fontWeight = FontWeight.Medium,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                color = colorResource(R.color.first_text_black),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = formatDate(file.dateModified),
                fontSize = 12.sp,
                lineHeight = 16.sp,
                color = colorResource(R.color.first_text_gray)
            )
        }

        Spacer(modifier = Modifier.width(4.dp))

        // File size
        Text(
            text = FileUtils.formatFileSize(file.size),
            fontSize = 12.sp,
            color = colorResource(R.color.second_text_gray),
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.width(8.dp))

        // Selection checkbox (always reserve space)
        Box(
            modifier = Modifier.size(18.dp),
            contentAlignment = Alignment.Center
        ) {
            if (isSelectionMode) {
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = Color.White,
                        modifier = Modifier
                            .size(18.dp)
                            .background(
                                color = colorResource(R.color.btn_orange),
                                shape = RoundedCornerShape(4.dp)
                            )
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .size(18.dp)
                            .background(
                                Color.Transparent,
                                RoundedCornerShape(4.dp)
                            )
                            .border(
                                width = 2.dp,
                                color = colorResource(R.color.btn_orange),
                                shape = RoundedCornerShape(4.dp)
                            )
                    )
                }
            }
        }
    }
}


private fun formatDate(timestamp: Long): String {
    val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.getDefault())
    return sdf.format(Date(timestamp))
}