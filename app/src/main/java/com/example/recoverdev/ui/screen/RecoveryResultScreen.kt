package com.example.recoverdev.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import com.example.recoverdev.R

@Composable
fun RecoveryResultScreen(
    onOkClick: () -> Unit
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("anim/finlish.json"))
    val progress by animateLottieCompositionAsState(
        composition = composition,
        iterations = 1
    )
    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.TopCenter
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Spacer(modifier = Modifier.height(60.dp))

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 40.dp)
                    .background(color = colorResource(R.color.main_background), RoundedCornerShape(27.dp))
                    .padding(30.dp)
                    .aspectRatio(1f)
            ){
                LottieAnimation(
                    composition = composition,
                    progress = { progress }
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Success text
            Text(
                text = stringResource(R.string.photos_recovered_successfully),
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color.Black,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // OK button
            Button(
                onClick = onOkClick,
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .height(48.dp),
                shape = RoundedCornerShape(8.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = colorResource(R.color.btn_orange)
                )
            ) {
                Text(
                    text = stringResource(R.string.ok),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                )
            }
        }
    }
}