package com.example.recoverdev.ui.screen

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.res.stringResource
import androidx.core.content.FileProvider
import com.example.recoverdev.R
import java.io.File

@Composable
fun SettingsScreenContent(
    modifier: Modifier = Modifier,
    currentLanguage: String = "English",
    onLanguageChanged: (String) -> Unit = {},
    appName: String? = null,
    appVersion: String? = null
) {
    val context = LocalContext.current
    var showLanguageDialog by remember { mutableStateOf(false) }
    
    // Get app name and version dynamically if not provided
    val finalAppName = appName ?: remember {
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            context.getString(R.string.app_name)
        } catch (e: PackageManager.NameNotFoundException) {
            "RecoverDev"
        }
    }
    
    val finalAppVersion = appVersion ?: remember {
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: PackageManager.NameNotFoundException) {
            "1.0.0"
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // App info card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = stringResource(R.string.app_description, finalAppName),
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        color = colorResource(R.color.first_text_black)
                    )

                }

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_logo),
                        contentDescription = "App Icon",
                        modifier = Modifier.size(60.dp)
                    )

                    Text(
                        text = stringResource(R.string.version, finalAppVersion),
                        fontSize = 12.sp,
                        color = colorResource(R.color.first_text_gray)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Settings items
        SettingsItem(
            icon = R.mipmap.set_lang,
            title = stringResource(R.string.language),
            onClick = { showLanguageDialog = true }
        )
        
        SettingsItem(
            icon = R.mipmap.set_contact,
            title = stringResource(R.string.contact_us),
            onClick = { 
                sendEmail(context)
            }
        )
        
        SettingsItem(
            icon = R.mipmap.set_share,
            title = stringResource(R.string.share_the_app),
            onClick = {
                shareApk(context)
            }
        )
        
        SettingsItem(
            icon = R.mipmap.set_privacy,
            title = stringResource(R.string.privacy_policy),
            onClick = { 
                openPrivacyPolicy(context)
            }
        )
        
        SettingsItem(
            icon = R.mipmap.set_terms,
            title = stringResource(R.string.terms_of_service),
            onClick = { 
                openTermsOfService(context)
            }
        )
    }
    
    // Language selection dialog
    if (showLanguageDialog) {
        LanguageSelectionDialog(
            currentLanguage = currentLanguage,
            onLanguageSelected = { language ->
                onLanguageChanged(language)
                showLanguageDialog = false
            },
            onDismiss = { showLanguageDialog = false }
        )
    }
}

@Composable
fun SettingsItem(
    icon: Int,
    title: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 6.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = icon),
                contentDescription = title,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = title,
                fontSize = 15.sp,
                color = colorResource(R.color.first_text_black),
                modifier = Modifier.weight(1f)
            )
            
            Image(
                painter = painterResource(id = R.mipmap.set_arrow),
                contentDescription = "Arrow",
                modifier = Modifier.size(18.dp)
            )
        }
    }
}

@Composable
fun LanguageSelectionDialog(
    currentLanguage: String,
    onLanguageSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    val languages = listOf(
        stringResource(R.string.english), 
        stringResource(R.string.deutsch), 
        stringResource(R.string.russian)
    )
    var selectedLanguage by remember { mutableStateOf(currentLanguage) }
    
    Dialog(onDismissRequest = {}) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.language),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.first_text_black)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                languages.forEach { language ->
                    LanguageItem(
                        language = language,
                        isSelected = language == selectedLanguage,
                        onClick = { selectedLanguage = language }
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Button(
                        onClick = {
                            onDismiss()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFDFDFDF)
                        ),
                        shape = RoundedCornerShape(10.dp),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(R.string.cancel),
                            color = colorResource(R.color.first_text_black)
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Button(
                        onClick = {
                            onLanguageSelected(selectedLanguage)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFFD8000)
                        ),
                        shape = RoundedCornerShape(10.dp),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(R.string.ok),
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun LanguageItem(
    language: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .background(
                if (isSelected) Color(0xFFFFEEDD) else Color(0xFFF6F6F6),
                RoundedCornerShape(4.dp)
            )
            .clickable { onClick() }
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = language,
            fontSize = 16.sp,
            color = if (isSelected) colorResource(R.color.btn_orange) else colorResource(R.color.second_text_gray),
            modifier = Modifier.weight(1f)
        )
        
        if (isSelected) {
            Text(
                text = "✓",
                fontSize = 18.sp,
                color = colorResource(R.color.btn_orange),
                fontWeight = FontWeight.Bold
            )
        }
    }
}

private fun sendEmail(context: Context) {
    val appName = context.getString(R.string.app_name)
    val intent = Intent(Intent.ACTION_SENDTO).apply {
        data = Uri.parse("mailto:")
        putExtra(Intent.EXTRA_EMAIL, arrayOf(context.getString(R.string.support_email)))
        putExtra(Intent.EXTRA_SUBJECT, context.getString(R.string.email_subject, appName))
    }
    
    try {
        context.startActivity(Intent.createChooser(intent, context.getString(R.string.send_email_chooser)))
    } catch (e: Exception) {
        // Handle case where no email app is available
    }
}

private fun shareApk(context: Context) {
    try {
        val srcApk = File(context.packageResourcePath)

        val destApk = File(context.cacheDir, "${context.packageName}.apk")
        srcApk.inputStream().use { input ->
            destApk.outputStream().use { output ->
                input.copyTo(output)
            }
        }

        val uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            destApk
        )

        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "application/vnd.android.package-archive"
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        context.grantUriPermission(
            context.packageName,
            uri,
            Intent.FLAG_GRANT_READ_URI_PERMISSION
        )

        context.startActivity(
            Intent.createChooser(
                shareIntent,
                "Share apk"
            )
        )
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun openPrivacyPolicy(context: Context) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(context.getString(R.string.privacy_policy_url)))
    try {
        context.startActivity(intent)
    } catch (e: Exception) {
        // Handle error
    }
}

private fun openTermsOfService(context: Context) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(context.getString(R.string.terms_of_service_url)))
    try {
        context.startActivity(intent)
    } catch (e: Exception) {
        // Handle error
    }
}
