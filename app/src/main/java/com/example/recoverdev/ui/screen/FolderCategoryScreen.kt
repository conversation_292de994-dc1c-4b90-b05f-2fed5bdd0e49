package com.example.recoverdev.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import androidx.compose.ui.unit.sp
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.FolderCategory
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.data.model.ScanResult
import com.example.recoverdev.R
import com.example.recoverdev.ui.components.GridFileItem

@Composable
fun FolderCategoryScreenContent(
    modifier: Modifier = Modifier,
    scanResult: ScanResult,
    onFolderClick: (FolderCategory) -> Unit,
    onAllFilesClick: (List<RecoverableFile>) -> Unit,
    onFileClick: (RecoverableFile) -> Unit
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(if (scanResult.fileType == FileType.OTHER) 8.dp else 16.dp)
    ) {
        if (scanResult.fileType == FileType.OTHER) {
            items(scanResult.folderCategories) { category ->
                SimpleFolderItem(
                    title = category.name,
                    fileCount = category.fileCount,
                    onClick = { onFolderClick(category) }
                )
            }
        } else {
            // All files section
            item {
                FolderCategoryItem(
                    title = stringResource(R.string.all),
                    fileCount = scanResult.totalFiles,
                    files = scanResult.allFiles.take(4),
                    onClick = { onAllFilesClick(scanResult.allFiles) },
                    onFileClick = onFileClick
                )
            }
            
            // Individual folder categories
            items(scanResult.folderCategories) { category ->
                FolderCategoryItem(
                    title = category.name,
                    fileCount = category.fileCount,
                    files = category.files.take(4),
                    onClick = { onFolderClick(category) },
                    onFileClick = onFileClick
                )
            }
        }
    }
}

@Composable
private fun SimpleFolderItem(
    title: String,
    fileCount: Int,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 80.dp)
            .padding(vertical = 4.dp)
            .background(color = Color.White, shape = RoundedCornerShape(10.dp))
            .padding(16.dp)
            .clickable { onClick() },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Folder icon
            Image(
                painter = painterResource(id = R.mipmap.type_folder),
                contentDescription = "Folder",
                modifier = Modifier.size(45.dp)
            )

            // Title and count
            Text(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(
                        color = colorResource(R.color.first_text_black),
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )) {
                        append(title)
                    }
                    withStyle(style = SpanStyle(
                        color = colorResource(R.color.btn_orange),
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )) {
                        append(" ($fileCount)")
                    }
                }
            )
        }

        // Arrow icon
        Image(
            painter = painterResource(id = R.mipmap.set_arrow),
            contentDescription = "Enter folder",
            modifier = Modifier.size(16.dp)
        )
    }
}

@Composable
private fun FolderCategoryItem(
    title: String,
    fileCount: Int,
    files: List<RecoverableFile>,
    onClick: () -> Unit,
    onFileClick: (RecoverableFile) -> Unit
) {
    Column(
        modifier = Modifier
            .clickable { onClick() }
    ) {
        // Header with title and arrow
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(color = colorResource(R.color.first_text_black),
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp
                    )) {
                        append(title)
                    }
                    withStyle(style = SpanStyle(color = colorResource(R.color.btn_orange),
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp)) {
                        append(" ($fileCount)")
                    }
                },
                textAlign = TextAlign.Center
            )

            Image(
                painter = painterResource(id = R.mipmap.set_arrow),
                contentDescription = "Enter folder",
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        if (files.isNotEmpty()) {
            val rows = files.chunked(4)
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                rows.forEach { rowFiles ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        rowFiles.forEach { file ->
                            GridFileItem(
                                file = file,
                                onClick = { onFileClick(file) },
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                            )
                        }
                        repeat(4 - rowFiles.size) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }
    }
}