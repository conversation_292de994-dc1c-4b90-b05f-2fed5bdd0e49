package com.example.recoverdev.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import com.example.recoverdev.R
import kotlinx.coroutines.delay

enum class ScanType {
    PHOTO, VIDEO, AUDIO, DOCUMENT, ALL
}

// Helper function to convert FileType to ScanType
fun fileTypeToScanType(fileType: com.example.recoverdev.data.model.FileType): ScanType {
    return when (fileType) {
        com.example.recoverdev.data.model.FileType.PHOTO -> ScanType.PHOTO
        com.example.recoverdev.data.model.FileType.VIDEO -> ScanType.VIDEO
        com.example.recoverdev.data.model.FileType.AUDIO -> ScanType.AUDIO
        com.example.recoverdev.data.model.FileType.OTHER -> ScanType.DOCUMENT
    }
}

@Composable
fun ScanningScreen(
    scanType: ScanType = ScanType.ALL,
    onScanComplete: () -> Unit,
    onStartScan: () -> Unit = {},
    currentScanPath: String = "",
    scanProgress: Float = 0f,
    isActuallyScanning: Boolean = false
) {
    var isScanning by remember { mutableStateOf(false) }
    var progress by remember { mutableStateOf(0f) }
    var scanningText by remember { mutableStateOf("") }
    
    val actualProgress = if (isActuallyScanning) scanProgress else progress
    val actualScanPath = if (isActuallyScanning && currentScanPath.isNotEmpty()) currentScanPath else ""
    
    // Get string resources
    val initializingScan = stringResource(R.string.initializing_scan)
    val scanningStorageDevices = stringResource(R.string.scanning_storage_devices)
    val analyzingFileSystem = stringResource(R.string.analyzing_file_system)
    val detectingDeletedFiles = stringResource(R.string.detecting_deleted_files)
    val verifyingFileIntegrity = stringResource(R.string.verifying_file_integrity)
    val scanAlmostComplete = stringResource(R.string.scan_almost_complete)
    

    
    // Get Lottie animation based on scan type
    val animationAsset = when (scanType) {
        ScanType.PHOTO -> "anim/home_photos.json"
        ScanType.VIDEO -> "anim/home_video.json"
        ScanType.AUDIO -> "anim/home_audio.json"
        ScanType.DOCUMENT -> "anim/home_other file.json"
        ScanType.ALL -> "anim/normal_scan.json"
    }
    
    // Lottie composition
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset(animationAsset))
    

    
    // Scan animation effect - only runs when user clicks scan button (not actual scanning)
    LaunchedEffect(isScanning) {
        if (isScanning && !isActuallyScanning) {
            android.util.Log.d("ScanningScreen", "Starting scan animation")
            
            val scanningSteps = listOf(
                Pair(initializingScan, 0.2f),
                Pair(scanningStorageDevices, 0.4f),
                Pair(analyzingFileSystem, 0.6f),
                Pair(detectingDeletedFiles, 0.8f),
                Pair(verifyingFileIntegrity, 1.0f)
            )
            
            onStartScan()
            
            scanningSteps.forEach { (text, targetProgress) ->
                scanningText = text
                progress = targetProgress
                delay(400) // 400ms per step
            }
            
            scanningText = scanAlmostComplete
            delay(500) // Brief pause before completing
            onScanComplete()
        }
    }
    
    // Handle actual scan completion
    LaunchedEffect(isActuallyScanning, scanProgress) {
        if (isActuallyScanning && scanProgress >= 1.0f) {
            // Actual scan completed, don't call onScanComplete here as it's handled by ViewModel
            scanningText = scanAlmostComplete
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Scan button/animation area

        Spacer(modifier = Modifier.height(60.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
                .clickable(enabled = !isScanning) {
                    if (!isScanning) {
                        isScanning = true
                        progress = 0f
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            if (isScanning || isActuallyScanning) {
                // Show Lottie animation when scanning
                LottieAnimation(
                    composition = composition,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // Show scan button when not scanning
                Image(
                    painter = painterResource(id = R.mipmap.img_scan_btn),
                    contentDescription = stringResource(R.string.start_scan),
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
        
        Spacer(modifier = Modifier.height(60.dp))
        
        Text(
            text = when {
                isActuallyScanning && actualScanPath.isNotEmpty() -> actualScanPath
                isScanning || isActuallyScanning -> scanningText.ifEmpty { stringResource(R.string.initializing_scan) }
                else -> stringResource(R.string.tap_to_scan)
            },
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.first_text_black),
            textAlign = TextAlign.Center
        )
    }
}