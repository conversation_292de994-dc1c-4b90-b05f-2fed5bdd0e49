package com.example.recoverdev.ui.screen

import android.annotation.SuppressLint
import android.media.MediaPlayer
import android.net.Uri
import android.widget.MediaController
import android.widget.VideoView
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.recoverdev.ui.components.DeleteConfirmationDialog
import androidx.compose.ui.viewinterop.AndroidView
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.ui.components.VideoThumbnail
import com.example.recoverdev.utils.FileOpenUtils
import com.example.recoverdev.utils.FileUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun FileDetailScreenContent(
    file: RecoverableFile,
    folderName: String? = null,
    isRecovered: Boolean = false,
    onRecoverClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    var isPlaying by remember { mutableStateOf(false) }
    var mediaPlayer by remember { mutableStateOf<MediaPlayer?>(null) }
    var currentPosition by remember { mutableStateOf(0L) }
    var duration by remember { mutableStateOf(0L) }
    var currentRotation by remember { mutableStateOf(0f) }
    val context = LocalContext.current

    LaunchedEffect(isPlaying) {
        if (isPlaying) {
            while (isPlaying) {
                val startTime = System.currentTimeMillis()
                val startRotation = currentRotation
                
                while (isPlaying) {
                    val elapsed = System.currentTimeMillis() - startTime
                    val progress = (elapsed % 10000) / 10000f
                    currentRotation = (startRotation + progress * 360f) % 360f
                    kotlinx.coroutines.delay(16)
                }
            }
        }
    }

    // Clean up resources
    DisposableEffect(Unit) {
        onDispose {
            mediaPlayer?.release()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        // Scrollable content with padding
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
                .padding(bottom = 80.dp) // Add bottom padding to avoid button overlap
        ) {
            // File content display based on type
            when (file.type) {
            FileType.PHOTO -> {
                // Image display with click to open
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .clickable {
                            FileOpenUtils.openFile(context, file)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(context)
                            .data(file.recoveredPath?.ifBlank { file.path } ?: file.path)
                            .crossfade(true)
                            .build(),
                        contentDescription = file.name,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
            }
            
            FileType.VIDEO -> {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .clickable {
                            FileOpenUtils.openFile(context, file)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    // Video thumbnail
                    VideoThumbnail(
                        file = File(file.recoveredPath?.ifBlank { file.path } ?: file.path),
                        contentDescription = "Video thumbnail",
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                    
                    // Play icon overlay
                    Box(
                        modifier = Modifier
                            .size(80.dp)
                            .background(Color.Black.copy(alpha = 0.5f), CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Play video",
                            tint = Color.White,
                            modifier = Modifier.size(48.dp)
                        )
                    }
                }
            }
            
            FileType.AUDIO -> {
                // Audio player display
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Audio icon with rotation
                    Image(
                        painter = painterResource(id = R.mipmap.pre_circle),
                        contentDescription = "Audio",
                        modifier = Modifier
                            .size(200.dp)
                            .rotate(currentRotation)
                    )

                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Progress bar
                    BoxWithConstraints(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    ) {
                        val maxWidthPx = maxWidth
                        val progress = if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            // Background track
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(4.dp)
                                    .background(
                                        Color(0xFFE4E4E4),
                                        RoundedCornerShape(2.dp)
                                    )
                            )
                            
                            // Progress track
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth(progress)
                                    .height(4.dp)
                                    .background(
                                        color = colorResource(R.color.btn_orange),
                                        RoundedCornerShape(2.dp)
                                    )
                            )
                            
                            // Progress circle indicator
                            Card(
                                shape = CircleShape,
                                modifier = Modifier.offset(x = maxWidthPx * progress - 4.dp),
                                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(15.dp)
                                        .border(1.dp, Color.White, CircleShape)
                                        .background(
                                            color = colorResource(R.color.btn_orange),
                                            CircleShape
                                        )
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Control buttons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(32.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Backward 15s
                        IconButton(
                            onClick = {
                                mediaPlayer?.let { player ->
                                    val newPosition = (player.currentPosition - 15000).coerceAtLeast(0)
                                    player.seekTo(newPosition)
                                    currentPosition = newPosition.toLong()
                                }
                            },
                            modifier = Modifier.size(48.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.mipmap.pre_left),
                                contentDescription = "Backward 15s",
                                modifier = Modifier.size(30.dp)
                            )
                        }
                        
                        // Play/Pause
                        IconButton(
                            onClick = {
                                val audioPath = file.recoveredPath ?: file.path
                                if (File(audioPath).exists()) {
                                    if (isPlaying) {
                                        mediaPlayer?.pause()
                                        isPlaying = false
                                    } else {
                                        try {
                                            if (mediaPlayer == null) {
                                                mediaPlayer = MediaPlayer().apply {
                                                    setDataSource(audioPath)
                                                    prepareAsync()
                                                    setOnPreparedListener {
                                                        start()
                                                        isPlaying = true
                                                        duration = this.duration.toLong()
                                                    }
                                                    setOnCompletionListener {
                                                        isPlaying = false
                                                        currentPosition = 0L
                                                        currentRotation = 0f
                                                    }
                                                }
                                            } else {
                                                mediaPlayer?.start()
                                                isPlaying = true
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }
                                }
                            },
                            modifier = Modifier
                                .size(64.dp)
                                .background(Color(0xFFFF8A00), CircleShape)
                        ) {
                            if (isPlaying) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_pause),
                                    contentDescription = "Pause",
                                    tint = Color.White,
                                    modifier = Modifier.size(32.dp)
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Default.PlayArrow,
                                    contentDescription = "Play",
                                    tint = Color.White,
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        }
                        
                        // Forward 15s
                        IconButton(
                            onClick = {
                                mediaPlayer?.let { player ->
                                    val newPosition = (player.currentPosition + 15000).coerceAtMost(player.duration)
                                    player.seekTo(newPosition)
                                    currentPosition = newPosition.toLong()
                                }
                            },
                            modifier = Modifier.size(48.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.mipmap.pre_right),
                                contentDescription = "Forward 15s",
                                modifier = Modifier.size(30.dp)
                            )
                        }
                    }
                }
            }
            
            FileType.OTHER -> {
                // Other file type with large icon
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.mipmap.pre_file),
                        contentDescription = "File",
                        modifier = Modifier.size(160.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // File information
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            FileInfoItem("Name:", file.name)
            FileInfoItem("Path:", file.recoveredPath ?: file.path)
            FileInfoItem("Size:", FileUtils.formatFileSize(file.size))
            FileInfoItem("Date:", formatDate(file.dateModified))
            
            if (file.format.isNotEmpty()) {
                FileInfoItem("Format:", file.format)
            }
            
            // Additional info for media files
            if (file.type == FileType.VIDEO || file.type == FileType.AUDIO) {
                file.duration?.let { duration ->
                    FileInfoItem("Duration:", formatDuration(duration))
                }
            }
            
            if (file.type == FileType.PHOTO || file.type == FileType.VIDEO) {
                file.dimension?.let { dimension ->
                    FileInfoItem("Dimension:", dimension)
                }
                file.resolution?.let { resolution ->
                    FileInfoItem("Resolution:", resolution)
                }
            }
        }
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        if (!isRecovered) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(horizontal = 60.dp, vertical = 12.dp)
                    .height(56.dp)
                    .align(Alignment.BottomCenter),
                contentAlignment = Alignment.Center
            ) {
                Button(
                    onClick = onRecoverClick,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    shape = RoundedCornerShape(12.dp), // Remove rounded corners for full width
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(R.color.btn_orange)
                    )
                ) {
                    Text(
                        text = stringResource(R.string.recover),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                }
            }
        }
    }
    
    // Update progress for audio
    LaunchedEffect(isPlaying) {
        if (isPlaying) {
            while (isPlaying) {
                mediaPlayer?.let { player ->
                    currentPosition = player.currentPosition.toLong()
                }
                kotlinx.coroutines.delay(1000)
            }
        }
    }
}

/**
 * FileDetailScreen function reserved for recovered file details page, including delete confirmation dialog
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FileDetailScreen(
    file: RecoverableFile,
    folderName: String? = null,
    onBackClick: () -> Unit,
    onRecoverClick: () -> Unit,
    onDeleteClick: () -> Unit,
    isRecovered: Boolean = false
) {
    var showDeleteDialog by remember { mutableStateOf(false) }

    // Delete confirmation dialog
    DeleteConfirmationDialog(
        showDialog = showDeleteDialog,
        onDismiss = { showDeleteDialog = false },
        onConfirm = onDeleteClick
    )

    FileDetailScreenContent(
        file = file,
        folderName = folderName,
        isRecovered = isRecovered,
        onRecoverClick = onRecoverClick,
        onDeleteClick = { showDeleteDialog = true }
    )
}

@Composable
private fun FileInfoItem(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = colorResource(R.color.first_text_black),
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(0.2f)
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = value,
            fontSize = 14.sp,
            color = colorResource(R.color.first_text_black),
            modifier = Modifier.weight(0.8f)
        )
    }
}

private fun formatDate(timestamp: Long): String {
    val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    return sdf.format(Date(timestamp))
}

private fun formatDuration(durationMs: Long): String {
    val seconds = durationMs / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    
    return when {
        hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
        else -> String.format("%d:%02d", minutes, seconds % 60)
    }
}