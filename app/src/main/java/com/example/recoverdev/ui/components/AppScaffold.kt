package com.example.recoverdev.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color

/**
 * Unified application scaffold component
 * Manages the overall page structure, including TopBar, content area, etc.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppScaffold(
    title: String,
    showBackButton: Boolean = true,
    onBackClick: (() -> Unit)? = null,
    topBarActions: @Composable RowScope.() -> Unit = {},
    content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
        topBar = {
            AppTopBar(
                title = title,
                showBackButton = showBackButton,
                onBackClick = onBackClick,
                actions = topBarActions
            )
        },
        containerColor = Color.Transparent,
        content = content
    )
}

/**
 * Home page specific scaffold component
 * Uses special home page TopBar style
 */
@Composable
fun HomeScaffold(
    onSettingsClick: () -> Unit,
    content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
        topBar = {
            HomeTopBar(onSettingsClick = onSettingsClick)
        },
        containerColor = Color.Transparent,
        content = content
    )
}

/**
 * Scaffold component without TopBar
 * Suitable for fullscreen pages or special pages
 */
@Composable
fun PlainScaffold(
    content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
        containerColor = Color.Transparent,
        content = content
    )
}