package com.example.recoverdev.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import java.io.File
import com.example.recoverdev.R

@Composable
fun FilePreviewItem(
    file: RecoverableFile,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showPlayIcon: Boolean = true
) {
    Card(
        modifier = modifier,
        onClick = onClick,
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Gray.copy(alpha = 0.1f)
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            when (file.type) {
                FileType.PHOTO -> {
                    PhotoPreview(file = file)
                }
                FileType.VIDEO -> {
                    VideoPreview(file = file, showPlayIcon = showPlayIcon)
                }
                FileType.AUDIO -> {
                    AudioPreview(file = file)
                }
                FileType.OTHER -> {
                    OtherFilePreview(file = file)
                }
            }
        }
    }
}

@Composable
private fun PhotoPreview(file: RecoverableFile) {
    val context = LocalContext.current
    
    val imagePath = file.recoveredPath ?: file.path
    
    if (File(imagePath).exists()) {
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(imagePath)
                .placeholder(R.mipmap.pre_image)
                .crossfade(true)
                .build(),
            contentDescription = file.name,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
    } else {
        FileTypeIcon(
            icon = Icons.Default.Home,
            format = file.format
        )
    }
}

@Composable
private fun VideoPreview(file: RecoverableFile, showPlayIcon: Boolean) {
    val context = LocalContext.current
    
    Box(modifier = Modifier.fillMaxSize()) {
        val videoPath = file.recoveredPath ?: file.path
        
        if (File(videoPath).exists()) {
            AsyncImage(
                model = ImageRequest.Builder(context)
                    .data(videoPath)
                    .placeholder(R.mipmap.pre_video)
                    .crossfade(true)
                    .build(),
                contentDescription = file.name,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        } else {
            FileTypeIcon(
                icon = Icons.Default.PlayArrow,
                format = file.format
            )
        }
        
        if (showPlayIcon) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Play",
                    tint = Color.White,
                    modifier = Modifier.size(32.dp)
                )
            }
        }
    }
}

@Composable
private fun AudioPreview(file: RecoverableFile) {
    FileTypeIcon(
        icon = Icons.Default.Star,
        format = file.format
    )
}

@Composable
private fun OtherFilePreview(file: RecoverableFile) {
    FileTypeIcon(
        icon = Icons.Default.Info,
        format = file.format
    )
}

@Composable
private fun FileTypeIcon(
    icon: ImageVector,
    format: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(32.dp),
            tint = Color.Gray
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = format,
            fontSize = 10.sp,
            color = Color.Gray
        )
    }
}