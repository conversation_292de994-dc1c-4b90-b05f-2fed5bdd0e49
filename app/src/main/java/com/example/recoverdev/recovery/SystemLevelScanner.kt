package com.example.recoverdev.recovery

import android.content.Context
import android.util.Log
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

class SystemLevelScanner(
    private val context: Context?,
    private val onScanPathUpdate: ((String) -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "SystemLevelScanner"
    }
    
    suspend fun performDeepScan(fileType: FileType): List<RecoverableFile> {
        return withContext(Dispatchers.IO) {
            val files = mutableListOf<RecoverableFile>()
            
            try {
                Log.d(TAG, "Starting system-level deep scan")
                
                // 1. Scan system databases
                scanSystemDatabases(files)
                
                // 2. Scan /data partition (requires root)
                scanDataPartition(files)
                
                // 3. Scan all mount points
                scanAllMountPoints(files)
                
                // 4. Find using shell commands
                scanWithShellCommands(files, fileType)
                
                // 5. Scan temporary files and cache
                scanTempAndCache(files)
                
                Log.d(TAG, "System-level deep scan completed, found ${files.size} files")
                
            } catch (e: Exception) {
                Log.e(TAG, "System-level scan failed", e)
            }
            
            files
        }
    }
    
    private suspend fun scanSystemDatabases(files: MutableList<RecoverableFile>) {
        try {
            Log.d(TAG, "Scanning system databases")
            
            val dbPaths = listOf(
                "/data/data/com.android.providers.media/databases",
                "/data/data/com.android.externalstorage/databases",
                "/data/system/packages.xml",
                "/data/system/users/0/package-restrictions.xml"
            )
            
            for (dbPath in dbPaths) {
                try {
                    val dbFile = File(dbPath)
                    if (dbFile.exists()) {
                        Log.d(TAG, "Found database file: $dbPath")
                        // TODO: Attempt to read database content here
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not access database: $dbPath", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to scan system databases", e)
        }
    }
    
    private suspend fun scanDataPartition(files: MutableList<RecoverableFile>) {
        try {
            Log.d(TAG, "Scanning /data partition")
            
            val dataPaths = listOf(
                "/data/media/0",
                "/data/user/0",
                "/data/data",
                "/data/app",
                "/data/system"
            )
            
            for (dataPath in dataPaths) {
                try {
                    val dataDir = File(dataPath)
                    if (dataDir.exists() && dataDir.canRead()) {
                        Log.d(TAG, "Scanning data directory: $dataPath")
                        scanDirectoryForDeletedFiles(dataDir, files, maxDepth = 2)
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not access data directory: $dataPath", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to scan /data partition", e)
        }
    }
    
    private suspend fun scanAllMountPoints(files: MutableList<RecoverableFile>) {
        try {
            Log.d(TAG, "Scanning all mount points")
            
            // Read /proc/mounts to get all mount points
            val mountsFile = File("/proc/mounts")
            if (mountsFile.exists() && mountsFile.canRead()) {
                mountsFile.readLines().forEach { line ->
                    val parts = line.split(" ")
                    if (parts.size >= 2) {
                        val mountPoint = parts[1]
                        if (mountPoint.startsWith("/storage") || mountPoint.startsWith("/mnt")) {
                            Log.d(TAG, "Checking mount point: $mountPoint")
                            scanMountPoint(mountPoint, files)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to scan mount points", e)
        }
    }
    
    private suspend fun scanMountPoint(mountPoint: String, files: MutableList<RecoverableFile>) {
        try {
            val mountDir = File(mountPoint)
            if (mountDir.exists() && mountDir.canRead()) {
                // Look for trash directories in each mount point
                val trashDirs = listOf(".trash", ".Trash", ".recycle", ".deleted")
                
                for (trashDirName in trashDirs) {
                    val trashDir = File(mountDir, trashDirName)
                    if (trashDir.exists() && trashDir.canRead()) {
                        Log.d(TAG, "Found trash in mount point $mountPoint: ${trashDir.absolutePath}")
                        scanDirectoryForDeletedFiles(trashDir, files)
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to scan mount point: $mountPoint", e)
        }
    }
    
    private suspend fun scanWithShellCommands(files: MutableList<RecoverableFile>, fileType: FileType) {
        try {
            Log.d(TAG, "Searching for files using shell commands")
            
            val extensions = getFileExtensions(fileType)
            
            for (extension in extensions.take(5)) { // Limit scan quantity
                try {
                    // Use find command to search for files with specific extensions
                    val command = "find /storage/emulated/0 -name '*.$extension' -type f 2>/dev/null"
                    val result = executeShellCommand(command)
                    
                    result.split("\n").forEach { filePath ->
                        if (filePath.isNotBlank()) {
                            val file = File(filePath.trim())
                            if (file.exists() && isInTrashLocation(filePath)) {
                                Log.d(TAG, "Shell command found file: $filePath")
                                val recoverableFile = createRecoverableFile(file, fileType)
                                files.add(recoverableFile)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Shell command execution failed: $extension", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Shell command scan failed", e)
        }
    }
    
    private suspend fun scanTempAndCache(files: MutableList<RecoverableFile>) {
        try {
            Log.d(TAG, "Scanning temporary files and cache")
            
            val tempPaths = listOf(
                "/storage/emulated/0/.temp",
                "/storage/emulated/0/.tmp",
                "/storage/emulated/0/temp",
                "/storage/emulated/0/tmp",
                "/storage/emulated/0/.cache",
                "/storage/emulated/0/cache",
                "/data/local/tmp",
                "/cache"
            )
            
            for (tempPath in tempPaths) {
                try {
                    val tempDir = File(tempPath)
                    if (tempDir.exists() && tempDir.canRead()) {
                        Log.d(TAG, "Scanning temporary directory: $tempPath")
                        scanDirectoryForDeletedFiles(tempDir, files, maxDepth = 1)
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to scan temporary directory: $tempPath", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to scan temporary files", e)
        }
    }
    
    private suspend fun scanDirectoryForDeletedFiles(
        directory: File,
        files: MutableList<RecoverableFile>,
        maxDepth: Int = 3,
        currentDepth: Int = 0
    ) {
        if (currentDepth >= maxDepth || files.size >= 200) return
        
        try {
            onScanPathUpdate?.invoke("${directory.absolutePath}")
            delay(50)

            directory.listFiles()?.forEach { file ->
                when {
                    file.isFile -> {
                        onScanPathUpdate?.invoke("${file.name}")
                        delay(50)

                        // Check if the file is possibly a deleted file
                        if (isPossiblyDeletedFile(file)) {
                            val recoverableFile = createRecoverableFile(file, FileType.OTHER)
                            files.add(recoverableFile)
                            Log.d(TAG, "Found possibly deleted file: ${file.name}")
                        }
                    }
                    file.isDirectory && currentDepth < maxDepth -> {
                        scanDirectoryForDeletedFiles(file, files, maxDepth, currentDepth + 1)
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to scan directory: ${directory.path}", e)
        }
    }
    
    private fun isPossiblyDeletedFile(file: File): Boolean {
        val fileName = file.name.lowercase()
        
        // Check if the file name contains deletion-related indicators
        val deletedIndicators = listOf(
            "deleted", "trash", "recycle", "removed", "backup", 
            ".bak", ".old", ".tmp", ".temp", "~"
        )
        
        return deletedIndicators.any { indicator ->
            fileName.contains(indicator)
        } || fileName.startsWith(".") // Hidden files might also be deleted
    }
    
    private fun isInTrashLocation(filePath: String): Boolean {
        val trashKeywords = listOf(
            "trash", "recycle", "deleted", "bin", "temp", "tmp", "cache"
        )
        
        return trashKeywords.any { keyword ->
            filePath.lowercase().contains(keyword)
        }
    }
    
    private fun executeShellCommand(command: String): String {
        return try {
            val process = Runtime.getRuntime().exec(command)
            val result = process.inputStream.bufferedReader().readText()
            process.waitFor()
            result
        } catch (e: Exception) {
            Log.w(TAG, "Command execution failed: $command", e)
            ""
        }
    }
    
    private fun createRecoverableFile(file: File, fileType: FileType): RecoverableFile {
        val normPath = normalizePath(file.absolutePath)
        val extension = getFileExtension(file.name)
        // Re-determine file type based on file name (including cleaned suffix)
        val actualFileType = determineFileTypeFromFileName(file.name)
        return RecoverableFile(
            id = UUID.randomUUID().toString(),
            name = file.name,
            path = normPath,
            size = file.length(),
            dateModified = file.lastModified(),
            type = actualFileType,
            format = extension.uppercase(),
            mimeType = getMimeType(extension),
            isRecovered = false,
            thumbnailPath = null
        )
    }
    
    /**
     * Determine file type based on extension
     */
    private fun determineFileTypeFromExtension(extension: String): FileType {
        return when (extension.lowercase()) {
            in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "heic", "raw", "tiff", "svg") -> FileType.PHOTO
            in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp", "m4v", "mpg", "mpeg", "rm", "rmvb") -> FileType.VIDEO
            in listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus", "amr", "mid", "midi") -> FileType.AUDIO
            else -> FileType.OTHER
        }
    }
    
    /**
     * Determine file type based on file name, handling special suffixes for deleted files
     */
    private fun determineFileTypeFromFileName(fileName: String): FileType {
        val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
        val extension = cleanFileName.substringAfterLast('.', "").lowercase()
        return determineFileTypeFromExtension(extension)
    }
    
    /**
     * Get file extension, handling _temp suffix added by manufacturers like OPPO
     */
    private fun getFileExtension(fileName: String): String {
        // Handle suffixes added by manufacturers like OPPO when deleting files
        val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
        return cleanFileName.substringAfterLast('.', "")
    }
    
    /**
     * Clean special suffixes of deleted files to restore original file name
     * Supports naming rules for deleted files on various phone brands
     */
    private fun cleanDeletedFileName(fileName: String): String {
        var cleanName = fileName
        
        Log.d(TAG, "Starting file name cleanup: '$fileName'")
        
        // OPPO/OnePlus ColorOS deleted file patterns
        val oppoPatterns = listOf(
            "_temp",
            "_deleted", 
            "_trash",
            "_recycle",
            "_backup"
        )
        
        // Xiaomi MIUI deleted file patterns
        val miuiPatterns = listOf(
            ".trashed",
            "_trashed",
            ".mi_deleted",
            "_mi_deleted",
            ".deleted"
        )
        
        // Huawei EMUI/HarmonyOS deleted file patterns
        val huaweiPatterns = listOf(
            ".hwdeleted",
            "_hwdeleted",
            ".huawei_trash",
            "_huawei_trash",
            ".emui_deleted"
        )
        
        // Vivo FuntouchOS/OriginOS deleted file patterns
        val vivoPatterns = listOf(
            ".vivo_deleted",
            "_vivo_deleted",
            ".funtouch_trash",
            "_funtouch_trash"
        )
        
        // Samsung One UI deleted file patterns
        val samsungPatterns = listOf(
            ".samsung_deleted",
            "_samsung_deleted",
            ".sec_trash",
            "_sec_trash",
            ".oneui_deleted"
        )
        
        // Meizu Flyme deleted file patterns
        val meizuPatterns = listOf(
            ".flyme_deleted",
            "_flyme_deleted",
            ".mz_trash",
            "_mz_trash"
        )
        
        // Realme UI deleted file patterns
        val realmePatterns = listOf(
            ".realme_deleted",
            "_realme_deleted",
            ".realme_trash",
            "_realme_trash"
        )
        
        // Generic deleted file patterns (various file managers)
        val genericPatterns = listOf(
            ".bak",
            ".backup",
            ".old",
            ".tmp",
            ".temp",
            "~",
            ".deleted",
            "_deleted",
            ".trash",
            "_trash",
            ".recycle",
            "_recycle"
        )
        
        // Timestamp patterns (some phones add timestamps when deleting files)
        val timestampPatterns = listOf(
            Regex("_\\\\d{10}$"),        // Unix timestamp suffix _1234567890
            Regex("_\\\\d{13}$"),        // Millisecond timestamp suffix _1234567890123
            Regex("\\\\.\\\\d{10}$"),      // Unix timestamp suffix .1234567890
            Regex("\\\\.\\\\d{13}$")       // Millisecond timestamp suffix .1234567890123
        )
        
        // Combine all suffix patterns
        val allSuffixPatterns = oppoPatterns + miuiPatterns + huaweiPatterns + 
                               vivoPatterns + samsungPatterns + meizuPatterns + 
                               realmePatterns + genericPatterns
        
        // Remove suffix patterns
        for (pattern in allSuffixPatterns) {
            if (cleanName.endsWith(pattern)) {
                cleanName = cleanName.removeSuffix(pattern)
                Log.d(TAG, "Removed suffix '$pattern': '$fileName' -> '$cleanName'")
                break
            }
        }
        
        // Remove timestamp patterns
        for (pattern in timestampPatterns) {
            if (pattern.containsMatchIn(cleanName)) {
                val match = pattern.find(cleanName)
                if (match != null) {
                    cleanName = cleanName.removeSuffix(match.value)
                    Log.d(TAG, "Removed timestamp suffix '${match.value}': '$fileName' -> '$cleanName'")
                    break
                }
            }
        }
        
        // Handle prefix patterns (some phones add identifiers to file names)
        val prefixPatterns = listOf(
            "deleted_",
            "trash_",
            "backup_",
            "temp_",
            "old_",
            "bak_"
        )
        
        for (pattern in prefixPatterns) {
            if (cleanName.startsWith(pattern)) {
                cleanName = cleanName.removePrefix(pattern)
                Log.d(TAG, "Removed prefix '$pattern': '$fileName' -> '$cleanName'")
                break
            }
        }
        
        Log.d(TAG, "File name cleanup completed: '$fileName' -> '$cleanName'")
        return cleanName
    }

    private fun normalizePath(path: String): String {
        return path
            .replace("/storage/emulated/0/", "/sdcard/")
            .replace("/storage/sdcard0/", "/sdcard/")
            .replace("/storage/sdcard1/", "/sdcard/")
            .replace("/mnt/sdcard/", "/sdcard/")
            .replace("/external_sd/", "/sdcard/")
            .replace("//", "/")
    }
    
    private fun getMimeType(extension: String): String? {
        return when (extension.lowercase()) {
            // Images
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            // Videos
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "wmv" -> "video/x-ms-wmv"
            "flv" -> "video/x-flv"
            // Audios
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            "aac" -> "audio/aac"
            "ogg" -> "audio/ogg"
            "wma" -> "audio/x-ms-wma"
            // Others
            "pdf" -> "application/pdf"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "txt" -> "text/plain"
            "zip" -> "application/zip"
            "apk" -> "application/vnd.android.package-archive"
            else -> null
        }
    }
    
    private fun getFileExtensions(fileType: FileType): List<String> {
        return when (fileType) {
            FileType.PHOTO -> listOf("jpg", "jpeg", "png", "gif", "bmp", "webp")
            FileType.VIDEO -> listOf("mp4", "avi", "mkv", "mov", "wmv", "flv")
            FileType.AUDIO -> listOf("mp3", "wav", "flac", "aac", "ogg", "wma")
            FileType.OTHER -> listOf("pdf", "doc", "docx", "txt", "zip", "apk")
        }
    }
}