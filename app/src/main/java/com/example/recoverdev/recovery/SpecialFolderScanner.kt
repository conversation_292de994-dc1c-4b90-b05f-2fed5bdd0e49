package com.example.recoverdev.recovery

import android.content.Context
import android.util.Log
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

class SpecialFolderScanner(private val context: Context?) {
    
    companion object {
        private const val TAG = "SpecialFolderScanner"
        
        // 特殊文件夹路径
        private val SPECIAL_FOLDERS = mapOf(
            "thumbnails" to listOf(
                "/storage/emulated/0/Android/data/com.android.providers.media/albumthumbs",
                "/storage/emulated/0/DCIM/.thumbnails", 
                "/storage/emulated/0/.thumbnails",
                "/data/data/com.android.providers.media/cache/thumbnails",
                "/sdcard/Android/data/com.android.providers.media/albumthumbs",
                "/storage/emulated/0/Android/data/com.google.android.apps.photos/cache/thumbnails",
                "/storage/emulated/0/Android/data/com.miui.gallery/cache/thumbnails",
                "/storage/emulated/0/Android/data/com.samsung.android.gallery3d/cache/thumbnails",
                "/storage/emulated/0/Pictures/.thumbnails"
            ),
            "cache" to listOf(
                "/storage/emulated/0/Android/data/*/cache",
                "/data/data/*/cache",
                "/storage/emulated/0/.cache",
                "/sdcard/.cache"
            ),
            "temp" to listOf(
                "/storage/emulated/0/.temp",
                "/storage/emulated/0/temp",
                "/data/local/tmp",
                "/cache"
            ),
            "hidden" to listOf(
                "/storage/emulated/0/.*",
                "/sdcard/.*"
            ),
            "recent" to listOf(
                "/storage/emulated/0/Android/data/com.android.providers.media/recent",
                "/data/data/com.android.providers.media/databases"
            )
        )
    }
    
    suspend fun scanSpecialFolders(fileType: FileType): List<RecoverableFile> {
        return withContext(Dispatchers.IO) {
            val files = mutableListOf<RecoverableFile>()
            
            try {
                Log.d(TAG, "开始扫描特殊文件夹，文件类型: $fileType")
                
                // 扫描缩略图文件夹
                files.addAll(scanThumbnailFolders(fileType))
                
                // 扫描缓存文件夹
                files.addAll(scanCacheFolders(fileType))
                
                // 扫描临时文件夹
                files.addAll(scanTempFolders(fileType))
                
                // 扫描隐藏文件夹
                files.addAll(scanHiddenFolders(fileType))
                
                // 扫描最近文件
                files.addAll(scanRecentFiles(fileType))
                
                Log.d(TAG, "特殊文件夹扫描完成，找到 ${files.size} 个文件")
                
            } catch (e: Exception) {
                Log.e(TAG, "扫描特殊文件夹失败", e)
            }
            
            files
        }
    }
    
    private fun scanThumbnailFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["thumbnails"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                Log.d(TAG, "检查缩略图文件夹: $folderPath, 存在: ${folder.exists()}, 是目录: ${folder.isDirectory}, 可读: ${folder.canRead()}")
                
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "扫描缩略图文件夹: $folderPath")
                    val foundFiles = scanFolderRecursively(folder, fileType, "thumbnails")
                    files.addAll(foundFiles)
                    Log.d(TAG, "在 $folderPath 中找到 ${foundFiles.size} 个文件")
                } else {
                    Log.d(TAG, "缩略图文件夹不存在或不可访问: $folderPath")
                }
            } catch (e: Exception) {
                Log.w(TAG, "无法访问缩略图文件夹: $folderPath", e)
            }
        }
        
        return files
    }
    
    private fun scanCacheFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["cache"]?.forEach { folderPath ->
            try {
                if (folderPath.contains("*")) {
                    // 处理通配符路径
                    val basePath = folderPath.substringBefore("*")
                    val baseFolder = File(basePath)
                    if (baseFolder.exists() && baseFolder.isDirectory) {
                        baseFolder.listFiles()?.forEach { subFolder ->
                            if (subFolder.isDirectory) {
                                val cachePath = folderPath.replace("*", subFolder.name)
                                val cacheFolder = File(cachePath)
                                if (cacheFolder.exists()) {
                                    files.addAll(scanFolderRecursively(cacheFolder, fileType, "cache"))
                                }
                            }
                        }
                    }
                } else {
                    val folder = File(folderPath)
                    if (folder.exists() && folder.isDirectory) {
                        Log.d(TAG, "扫描缓存文件夹: $folderPath")
                        files.addAll(scanFolderRecursively(folder, fileType, "cache"))
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "无法访问缓存文件夹: $folderPath", e)
            }
        }
        
        return files
    }
    
    private fun scanTempFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["temp"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "扫描临时文件夹: $folderPath")
                    files.addAll(scanFolderRecursively(folder, fileType, "temp"))
                }
            } catch (e: Exception) {
                Log.w(TAG, "无法访问临时文件夹: $folderPath", e)
            }
        }
        
        return files
    }
    
    private fun scanHiddenFolders(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        try {
            val sdcardPath = "/storage/emulated/0"
            val sdcard = File(sdcardPath)
            if (sdcard.exists() && sdcard.isDirectory) {
                sdcard.listFiles()?.forEach { file ->
                    if (file.isDirectory && file.name.startsWith(".") && file.canRead()) {
                        Log.d(TAG, "扫描隐藏文件夹: ${file.absolutePath}")
                        files.addAll(scanFolderRecursively(file, fileType, "hidden", maxDepth = 2))
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "无法扫描隐藏文件夹", e)
        }
        
        return files
    }
    
    private fun scanRecentFiles(fileType: FileType): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        SPECIAL_FOLDERS["recent"]?.forEach { folderPath ->
            try {
                val folder = File(folderPath)
                if (folder.exists() && folder.isDirectory) {
                    Log.d(TAG, "扫描最近文件夹: $folderPath")
                    files.addAll(scanFolderRecursively(folder, fileType, "recent"))
                }
            } catch (e: Exception) {
                Log.w(TAG, "无法访问最近文件夹: $folderPath", e)
            }
        }
        
        return files
    }
    
    private fun scanFolderRecursively(
        folder: File, 
        fileType: FileType, 
        folderCategory: String,
        maxDepth: Int = 3,
        currentDepth: Int = 0
    ): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        if (currentDepth >= maxDepth || !folder.canRead()) {
            return files
        }
        
        try {
            folder.listFiles()?.forEach { file ->
                if (file.isFile && isMatchingFileType(file, fileType)) {
                    val recoverableFile = createRecoverableFile(file, folderCategory)
                    files.add(recoverableFile)
                } else if (file.isDirectory && currentDepth < maxDepth - 1) {
                    files.addAll(scanFolderRecursively(file, fileType, folderCategory, maxDepth, currentDepth + 1))
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "扫描文件夹失败: ${folder.absolutePath}", e)
        }
        
        return files
    }
    
    private fun isMatchingFileType(file: File, fileType: FileType): Boolean {
        val extension = file.extension.lowercase()
        
        return when (fileType) {
            FileType.PHOTO -> {
                extension in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "raw", "heic", "heif")
            }
            FileType.VIDEO -> {
                extension in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts")
            }
            FileType.AUDIO -> {
                extension in listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus")
            }
            FileType.OTHER -> {
                extension !in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "raw", "heic", "heif",
                                   "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts",
                                   "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus")
            }
        }
    }
    
    private fun createRecoverableFile(file: File, folderCategory: String): RecoverableFile {
        val extension = file.extension.lowercase()
        val fileType = when {
            extension in listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "raw", "heic", "heif") -> FileType.PHOTO
            extension in listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "ts") -> FileType.VIDEO
            extension in listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus") -> FileType.AUDIO
            else -> FileType.OTHER
        }
        
        return RecoverableFile(
            id = "${folderCategory}_${file.absolutePath.hashCode()}_${System.currentTimeMillis()}",
            name = file.name,
            path = file.absolutePath,
            size = file.length(),
            dateModified = file.lastModified(),
            type = fileType,
            format = extension,
            mimeType = getMimeType(extension)
        )
    }
    
    private fun getMimeType(extension: String): String {
        return when (extension.lowercase()) {
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            else -> "application/octet-stream"
        }
    }
}