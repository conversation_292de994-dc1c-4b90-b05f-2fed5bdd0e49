package com.example.recoverdev.recovery

import android.content.Context
import android.content.ContentResolver
import android.database.Cursor
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

class FileRecoveryEngine(
    private val context: Context? = null,
    private val onScanPathUpdate: ((String) -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "FileRecoveryEngine"
        // Cache for recovered files, to avoid duplicate display
        private val recoveredFileIds = mutableSetOf<String>()
    }
    
    suspend fun scanDeletedFiles(fileType: FileType): List<RecoverableFile> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting scan for deleted files in recycle bin, type: $fileType")
                
                val allFiles = mutableSetOf<RecoverableFile>()
                
                if (context == null) {
                    Log.w(TAG, "Context is null, cannot scan")
                    return@withContext emptyList()
                }
                
                // Merge methods 1 and 2: System database scan
                val systemDatabaseFiles = scanSystemDatabase()
                allFiles.addAll(systemDatabaseFiles)
                Log.d(TAG, "System database scan found ${systemDatabaseFiles.size} files")
                
                // Merge methods 3 and 4: File system location scan
                val fileSystemFiles = scanFileSystemLocations()
                allFiles.addAll(fileSystemFiles)
                Log.d(TAG, "File system location scan found ${fileSystemFiles.size} files")
                
                // New: Scan special folders (thumbnails, hidden files, etc.)
                val specialFolderScanner = SpecialFolderScanner(context)
                val specialFolderFiles = specialFolderScanner.scanSpecialFolders(fileType)
                allFiles.addAll(specialFolderFiles)
                Log.d(TAG, "Special folder scan found ${specialFolderFiles.size} files")
                
                // Method 6: System-level deep scan
                val systemScanner = SystemLevelScanner(context)
                val systemFiles = systemScanner.performDeepScan(fileType)
                allFiles.addAll(systemFiles)
                Log.d(TAG, "System-level scan found ${systemFiles.size} files")
                
                // Deduplicate: based on normalized path and size
                val uniqueFiles = allFiles.distinctBy { "${normalizePath(it.path)}_${it.size}" }
                
                // Filter by file type - use actual file type instead of recalculating
                val filteredFiles = uniqueFiles.filter { file ->
                    val shouldInclude = when (fileType) {
                        FileType.PHOTO -> file.type == FileType.PHOTO
                        FileType.VIDEO -> file.type == FileType.VIDEO
                        FileType.AUDIO -> file.type == FileType.AUDIO
                        FileType.OTHER -> file.type == FileType.OTHER && !isTempOrCacheFile(file.name, file.path)
                    }
                    
                    // Add debug logs, especially for files containing _temp
                    if (file.name.contains("_temp") || file.name.contains("_deleted")) {
                        Log.d(TAG, "Filter check: File='${file.name}', Actual type=${file.type}, Requested type=$fileType, Should include=$shouldInclude")
                    }
                    
                    shouldInclude
                }
                
                Log.d(TAG, "Found ${allFiles.size} total files, ${uniqueFiles.size} after deduplication, ${filteredFiles.size} filtered $fileType files")
                return@withContext filteredFiles
                
            } catch (e: Exception) {
                Log.e(TAG, "Error during scan", e)
                throw Exception("Scan failed: ${e.message}")
            }
        }
    }
    
    /**
     * Merged method: Scan system databases (MediaStore + ContentResolver)
     */
    private suspend fun scanSystemDatabase(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        // Original scanAllTrashedFiles logic
        files.addAll(scanAllTrashedFiles())
        
        // Original scanWithContentResolver logic
        files.addAll(scanWithContentResolver())
        
        return files
    }
    
    /**
     * Merged method: scan file system locations (recycle bin directories + vendor-specific locations)
     */
    private suspend fun scanFileSystemLocations(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        // Original scanFileSystemTrash logic
//        files.addAll(scanFileSystemTrash())
        
        // Original scanVendorSpecificTrash logic
        files.addAll(scanVendorSpecificTrash())
        
        return files
    }
    
    /**
     * Simple scan of all recycle bin files, without complex filtering
     */
    private fun scanAllTrashedFiles(): List<RecoverableFile> {
        if (context == null) return emptyList()
        
        val files = mutableListOf<RecoverableFile>()
        
        // 1. Scan MediaStore.Files table (contains all file types)
        try {
            val contentResolver = context.contentResolver
            val uri = MediaStore.Files.getContentUri("external")
            
            val projection = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.DATA,
                MediaStore.Files.FileColumns.SIZE,
                MediaStore.Files.FileColumns.DATE_MODIFIED,
                MediaStore.Files.FileColumns.MIME_TYPE
            )
            
            // Add IS_TRASHED column (if supported)
            val projectionWithTrash = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                projection + MediaStore.Files.FileColumns.IS_TRASHED
            } else {
                projection
            }
            
            val cursor = when {
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R -> {
                    val args = Bundle().apply {
                        putInt("android:query-arg-match-trashed", 1)
                        putInt("android:query-arg-match-pending", 1)
                        // No MIME type restriction, scan all files
                        putString("android:query-arg-sql-sort-order", "${MediaStore.Files.FileColumns.DATE_MODIFIED} DESC")
                    }
                    Log.d(TAG, "Using Android 11+ Bundle query")
                    queryTrashedWithBundleCompat(contentResolver, uri, projectionWithTrash, args)
                }
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q -> {
                    Log.d(TAG, "Using Android 10 IS_TRASHED query")
                    contentResolver.query(
                        uri,
                        projectionWithTrash,
                        "${MediaStore.Files.FileColumns.IS_TRASHED} = ?",
                        arrayOf("1"),
                        "${MediaStore.Files.FileColumns.DATE_MODIFIED} DESC"
                    )
                }
                else -> {
                    Log.d(TAG, "Android 9 and below do not support recycle bin queries")
                    null
                }
            }
            
            cursor?.use { c ->
                Log.d(TAG, "Found ${c.count} records")
                
                val dataColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA)
                val nameColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME)
                val sizeColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.SIZE)
                val dateColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
                val idColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
                val trashedColumn = c.getColumnIndex(MediaStore.Files.FileColumns.IS_TRASHED)
                
                while (c.moveToNext()) {
                    val filePath = c.getString(dataColumn) ?: continue
                    val fileName = c.getString(nameColumn) ?: File(filePath).name
                    val isTrashed = if (trashedColumn >= 0) {
                        c.getInt(trashedColumn) == 1
                    } else {
                        true // For versions that don't support IS_TRASHED, assume all are recycle bin files
                    }
                    
                    Log.d(TAG, "Checking file: $fileName, path: $filePath, trashed: $isTrashed")
                    
                    if (isTrashed) {
                        val fileId = c.getLong(idColumn).toString()
                        if (!recoveredFileIds.contains(fileId)) {
                            val normPath = normalizePath(filePath)
                            val extension = getFileExtension(normPath)
                            val recoverable = RecoverableFile(
                                id = fileId,
                                name = fileName,
                                path = normPath,
                                size = c.getLong(sizeColumn),
                                dateModified = c.getLong(dateColumn) * 1000,
                                type = determineFileType(fileName),
                                format = extension.uppercase(),
                                mimeType = getMimeType(extension),
                                isRecovered = false,
                                recoveredPath = null,
                                thumbnailPath = generateThumbnailPath(normPath, fileName)
                            )
                            files.add(recoverable)
                            Log.d(TAG, "Adding recycle bin file: $fileName")
                        } else {
                            Log.d(TAG, "Skipping already recovered file: $fileName")
                        }
                    }
                }
            } ?: Log.w(TAG, "empty")
            
        } catch (e: Exception) {
            Log.e(TAG, "MediaStore.Files file", e)
        }
        
        // 2. If MediaStore scan results are empty, try scanning file system recycle bin directories
        if (files.isEmpty()) {
            Log.d(TAG, "MediaStore scan returned no results, trying file system scan")
            files.addAll(scanFileSystemTrash())
        }
        
        Log.d(TAG, "Total found ${files.size} recycle bin files")
        return files
    }
    
    /**
     * Scan file system recycle bin directories
     */
    private fun scanFileSystemTrash(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        val storagePaths = listOf(
            "/storage/emulated/0",
            "/storage/emulated/1", 
            "/sdcard",
            "/mnt/sdcard"
        )
        
        val trashPatterns = listOf(
            ".Trash-",
            ".trash",
            ".Trash",
            "Recycle Bin",
            ".RecycleBin",
            "Trash",
            ".recently-deleted",
            "Recently Deleted",
            ".FileManagerRecycler"
        )
        
        storagePaths.forEach { storagePath ->
            try {
                val storageDir = File(storagePath)
                if (storageDir.exists() && storageDir.canRead()) {
                    onScanPathUpdate?.invoke(storagePath)
                    
                    storageDir.listFiles()?.forEach { subDir ->
                        if (subDir.isDirectory) {
                            val dirName = subDir.name
                            val isTrashDir = trashPatterns.any { pattern ->
                                when {
                                    pattern == ".Trash-" -> dirName.contains(".Trash-")
                                    else -> dirName == pattern
                                }
                            }
                            
                            if (isTrashDir && subDir.canRead()) {
                                Log.d(TAG, "找到回收站目录: ${subDir.absolutePath}")
                                scanDirectoryRecursively(subDir, files, maxDepth = 10, maxFiles = 500)
                            }
                        }
                    }
                    
                    // 也扫描一些固定的子路径
                    val fixedSubPaths = listOf(
                        ".local/share/Trash/files",
                        "Android/data/com.android.documentsui/files/Trash"
                    )
                    
                    fixedSubPaths.forEach { subPath ->
                        val dir = File(storageDir, subPath)
                        if (dir.exists() && dir.canRead()) {
                            Log.d(TAG, "扫描固定回收站路径: ${dir.absolutePath}")
                            scanDirectoryRecursively(dir, files, maxDepth = 10, maxFiles = 500)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "无法访问存储路径: $storagePath - ${e.message}")
            }
        }
        
        return files
    }
    
    /**
     * 递归扫描目录
     */
    private fun scanDirectoryRecursively(
        dir: File, 
        files: MutableList<RecoverableFile>, 
        maxDepth: Int = 10, 
        currentDepth: Int = 0,
        maxFiles: Int = 500
    ) {
        if (currentDepth >= maxDepth || files.size >= maxFiles) return
        
        onScanPathUpdate?.invoke(dir.absolutePath)
        
        try {
            dir.listFiles()?.forEach { file ->
                when {
                    file.isFile -> {
                        val normPath = normalizePath(file.absolutePath)
                        val extension = getFileExtension(file.name)
                        val recoverable = RecoverableFile(
                            id = UUID.randomUUID().toString(),
                            name = file.name,
                            path = normPath,
                            size = file.length(),
                            dateModified = file.lastModified(),
                            type = determineFileType(file.name), // 使用正确的文件类型判断
                            format = extension.uppercase(),
                            mimeType = getMimeType(extension),
                            isRecovered = false,
                            recoveredPath = null,
                            thumbnailPath = null
                        )
                        files.add(recoverable)
                        Log.d(TAG, "文件系统找到: ${file.name}, 类型=${recoverable.type}, MIME=${recoverable.mimeType}")
                    }
                    file.isDirectory && currentDepth < maxDepth -> {
                        scanDirectoryRecursively(file, files, maxDepth, currentDepth + 1, maxFiles)
                    }
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "扫描目录失败: ${dir.path} - ${e.message}")
        }
    }
    
    private suspend fun scanWithContentResolver(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        try {
            Log.d(TAG, "开始 ContentResolver 扫描")
            
            // 尝试不同的 URI 和查询方式
            val uris = listOf(
                MediaStore.Files.getContentUri("external"),
                MediaStore.Files.getContentUri("internal")
            )
            
            for (uri in uris) {
                try {
                    context?.contentResolver?.query(
                        uri,
                        null,
                        null,
                        null,
                        null
                    )?.use { cursor ->
                        Log.d(TAG, "URI $uri 返回 ${cursor.count} 条记录")
                        
                        // 打印所有可用的列名
                        val columnNames = cursor.columnNames
                        Log.d(TAG, "可用列: ${columnNames.joinToString(", ")}")
                        
                        var count = 0
                        while (cursor.moveToNext() && count < 10) { // 只处理前10条作为示例
                            try {
                                val nameIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DISPLAY_NAME)
                                val pathIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DATA)
                                val sizeIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.SIZE)
                                
                                if (nameIndex >= 0 && pathIndex >= 0) {
                                    val name = cursor.getString(nameIndex) ?: "未知文件"
                                    val path = cursor.getString(pathIndex) ?: ""
                                    val size = if (sizeIndex >= 0) cursor.getLong(sizeIndex) else 0L
                                    
                                    Log.d(TAG, "ContentResolver 找到文件: $name, 路径: $path")
                                }
                            } catch (e: Exception) {
                                Log.w(TAG, "处理 ContentResolver 记录时出错", e)
                            }
                            count++
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "查询 URI $uri 失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "ContentResolver 扫描失败", e)
        }
        
        return files
    }

    private suspend fun scanVendorSpecificTrash(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        try {
            Log.d(TAG, "开始厂商特定回收站扫描")
            
            // 定义回收站目录名称模式（不包含完整路径）
            val trashDirNames = listOf(
                // 通用回收站目录名
                ".Trash-",           // 匹配 .Trash-1000, .Trash-1001 等
                ".Trash",
                ".trash",
                "Trash",
                "trash",
                ".recycle",
                "recycle",
                ".recycleBin",
                "recycle_bin",
                "Recycle Bin",
                ".deleted",
                ".Deleted",
                "Recently Deleted",
                ".recently-deleted",
                "recently_delete",
                
                // 小米 MIUI
                ".trashBin",
                ".mi_trash",
                ".MIUITrash",
                ".miTrash",
                ".xiaomiTrash",
                
                // 华为 EMUI/HarmonyOS
                ".hwRecycleBin",
                "HwRecycleBin",
                ".huawei_trash",
                
                // OPPO ColorOS
                ".oppoTrash",
                ".oppoTmp",
                ".oppo_recycle",
                ".colorosTrash",
                ".oppoRecycle",
                
                // Vivo FuntouchOS/OriginOS
                ".vivoTrash",
                ".vivo_recycle",
                ".vivoRecycle",
                ".vivoRecycleBin",
                
                // 三星 One UI
                ".samsung_trash",
                ".samsungTrash",
                ".sec_trash",
                
                // 一加 OxygenOS
                ".oneplusTrash",
                ".oneplus_recycle",
                
                // 魅族 Flyme
                ".meizu_trash",
                ".mzTrash",
                
                // Realme
                ".realmeRecycle",
                
                // 联想
                ".lenovo_trash",
                
                // 其他
                ".FileManagerRecycler"
            )
            
            // 扫描所有存储位置
            val storagePaths = listOf(
                "/storage/emulated/0",
                "/storage/emulated/1", 
                "/data/media/0",
                "/sdcard"
            )
            
            // 递归扫描所有目录，查找包含回收站名称的目录
            storagePaths.forEach { storagePath ->
                try {
                    val storageDir = File(storagePath)
                    if (storageDir.exists() && storageDir.canRead()) {
                        // 报告当前扫描的存储路径
                        onScanPathUpdate?.invoke(storagePath)
                        scanForTrashDirectories(storageDir, trashDirNames, files, maxDepth = 10)
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "扫描存储路径失败: $storagePath", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "厂商回收站扫描失败", e)
        }
        
        return files
    }
    
    /**
     * 递归扫描目录，查找包含回收站名称的目录
     */
    private fun scanForTrashDirectories(
        dir: File,
        trashDirNames: List<String>,
        files: MutableList<RecoverableFile>,
        maxDepth: Int = 10,
        currentDepth: Int = 0,
        maxFiles: Int = 500
    ) {
        if (currentDepth >= maxDepth || files.size >= maxFiles) return
        
        try {
            dir.listFiles()?.forEach { subDir ->
                if (subDir.isDirectory && subDir.canRead()) {
                    val dirName = subDir.name
                    val dirPath = subDir.absolutePath
                    
                    // 检查目录名是否匹配回收站模式
                    val isTrashDir = trashDirNames.any { pattern ->
                        when {
                            pattern == ".Trash-" -> dirName.contains(".Trash-")
                            else -> dirName == pattern || dirPath.contains("/$pattern/")
                        }
                    }
                    
                    if (isTrashDir) {
                        Log.d(TAG, "发现回收站目录: ${subDir.absolutePath}")
                        scanDirectoryRecursively(subDir, files, maxDepth = 10)
                    } else {
                        // 继续递归扫描子目录
                        scanForTrashDirectories(subDir, trashDirNames, files, maxDepth, currentDepth + 1, maxFiles)
                    }
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "扫描目录失败: ${dir.path} - ${e.message}")
        }
    }
    
    /**
     * 标准化路径，将不同的根路径统一为标准格式
     */
    private fun normalizePath(path: String): String {
        return path
            .replace("/storage/emulated/0/", "/sdcard/")
            .replace("/storage/sdcard0/", "/sdcard/")
            .replace("/storage/sdcard1/", "/sdcard/")
            .replace("/mnt/sdcard/", "/sdcard/")
            .replace("/external_sd/", "/sdcard/")
            .replace("//", "/") // 移除双斜杠
    }
    
    /**
     * 获取文件扩展名，处理 OPPO 等厂商添加的 _temp 后缀
     */
    private fun getFileExtension(fileName: String): String {
        // 处理 OPPO 等厂商在删除文件时添加的后缀
        val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
        return cleanFileName.substringAfterLast('.', "")
    }

    /**
     * 根据文件扩展名获取 MIME 类型
     */
    private fun getMimeType(extension: String): String? {
        return when (extension.lowercase()) {
            // 图片
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            "heic" -> "image/heic"
            "raw" -> "image/x-canon-cr2"
            "tiff" -> "image/tiff"
            "svg" -> "image/svg+xml"
            // 视频
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "wmv" -> "video/x-ms-wmv"
            "flv" -> "video/x-flv"
            "webm" -> "video/webm"
            "3gp" -> "video/3gpp"
            "m4v" -> "video/x-m4v"
            "mpg", "mpeg" -> "video/mpeg"
            "rm" -> "application/vnd.rn-realmedia"
            "rmvb" -> "application/vnd.rn-realmedia-vbr"
            // 音频
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            "aac" -> "audio/aac"
            "ogg" -> "audio/ogg"
            "wma" -> "audio/x-ms-wma"
            "m4a" -> "audio/mp4"
            "opus" -> "audio/opus"
            "amr" -> "audio/amr"
            "mid", "midi" -> "audio/midi"
            // 其他
            "pdf" -> "application/pdf"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "txt" -> "text/plain"
            "zip" -> "application/zip"
            "apk" -> "application/vnd.android.package-archive"
            else -> null
        }
    }
    
    /**
     * 判断文件是否为媒体文件（图片、视频、音频）
     */
    private fun isMediaFile(fileName: String): Boolean {
        val extension = getFileExtension(fileName).lowercase()
        val photoExtensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "heic", "raw", "tiff", "svg")
        val videoExtensions = listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp", "m4v", "mpg", "mpeg", "rm", "rmvb")
        val audioExtensions = listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus", "amr", "mid", "midi")
        val allMediaExtensions = photoExtensions + videoExtensions + audioExtensions
        return allMediaExtensions.contains(extension)
    }
    
    /**
     * 判断文件是否为临时文件或缓存文件
     */
    private fun isTempOrCacheFile(fileName: String, filePath: String): Boolean {
        val lowerFileName = fileName.lowercase()
        val lowerFilePath = filePath.lowercase()
        
        // 只过滤明显的系统临时文件，保留用户文档
        val systemTempIndicators = listOf(".tmp", ".cache", "~")
        val hasSystemTempName = systemTempIndicators.any { indicator -> lowerFileName.endsWith(indicator) }
        
        // 只过滤系统级临时目录，不过滤用户目录
        val systemTempPaths = listOf("/.cache/", "/cache/", "/data/local/tmp/", "/tmp/")
        val isInSystemTempPath = systemTempPaths.any { path -> lowerFilePath.contains(path) }
        
        // 过滤明显的垃圾文件
        val isJunkFile = lowerFileName.startsWith("thumbs.db") || 
                        lowerFileName.startsWith(".ds_store") ||
                        lowerFileName.endsWith(".log") && lowerFilePath.contains("/log/")
        
        return hasSystemTempName || isInSystemTempPath || isJunkFile
    }
    
    /**
     * 根据文件名确定文件类型，处理删除文件的特殊后缀
     */
    private fun determineFileType(fileName: String): FileType {
        // 使用清理后的文件名获取扩展名
        val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
        val extension = cleanFileName.substringAfterLast('.', "").lowercase()
        
        val fileType = when {
            listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "heic", "raw", "tiff", "svg").contains(extension) -> FileType.PHOTO
            listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp", "m4v", "mpg", "mpeg", "rm", "rmvb").contains(extension) -> FileType.VIDEO
            listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus", "amr", "mid", "midi").contains(extension) -> FileType.AUDIO
            else -> FileType.OTHER
        }
        
        // 添加调试日志
        if (fileName != cleanFileName) {
            Log.d(TAG, "文件类型判断: 原文件名='$fileName', 清理后='$cleanFileName', 扩展名='$extension', 类型=$fileType")
        }
        
        return fileType
    }

    /**
     * 生成缩略图路径（如果文件存在且是媒体文件）
     */
    private fun generateThumbnailPath(filePath: String, fileName: String): String? {
        val file = File(filePath)
        if (!file.exists()) return null
        
        val extension = getFileExtension(fileName).lowercase()
        return when {
            listOf("jpg", "jpeg", "png", "gif", "bmp", "webp").contains(extension) -> filePath
            listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp").contains(extension) -> filePath
            else -> null
        }
    }
    
    suspend fun recoverFile(file: RecoverableFile): String {
        return withContext(Dispatchers.IO) {
            try {
                if (context == null) {
                    throw Exception("Context为空，无法恢复文件")
                }
                
                // 方法1：如果源文件路径存在，直接复制
                val sourceFile = File(file.path)
                if (sourceFile.exists() && sourceFile.canRead()) {
                    return@withContext recoverFromFilePath(sourceFile, file)
                }
                
                // 方法2：通过 MediaStore ContentUri 恢复
                return@withContext recoverFromMediaStore(file)
                
            } catch (e: Exception) {
                Log.e(TAG, "文件恢复失败", e)
                throw Exception("文件恢复失败: ${e.message}")
            }
        }
    }
    
    /**
     * 从文件路径恢复 - 原子操作版本
     */
    private fun recoverFromFilePath(sourceFile: File, file: RecoverableFile): String {
        // 创建恢复目录
        val recoveryDir = File("/storage/emulated/0/RecoveredFiles")
        if (!recoveryDir.exists()) {
            recoveryDir.mkdirs()
        }
        
        // 使用清理后的文件名作为恢复后的文件名
        val cleanFileName = FileUtils.cleanDeletedFileName(file.name)
        val targetFile = File(recoveryDir, cleanFileName)
        
        // 如果目标文件已存在，添加数字后缀避免覆盖
        val finalTargetFile = getUniqueFileName(targetFile)
        
        // 创建临时文件，确保原子操作
        val tempFile = File(finalTargetFile.parentFile, "${finalTargetFile.name}.tmp")
        
        try {
            // 步骤1：先复制到临时文件
            sourceFile.copyTo(tempFile, overwrite = true)
            
            // 步骤2：验证复制是否成功
            if (!tempFile.exists() || tempFile.length() != sourceFile.length()) {
                throw Exception("文件复制验证失败")
            }
            
            // 步骤3：原子性重命名临时文件为最终文件
            if (!tempFile.renameTo(finalTargetFile)) {
                throw Exception("临时文件重命名失败")
            }
            
            // 步骤4：只有在文件成功恢复后才删除原文件和标记
            val sourceDeleted = sourceFile.delete()
            if (sourceDeleted) {
                markFileAsRecovered(file)
                Log.d(TAG, "原子操作恢复成功: 原文件已删除")
            } else {
                Log.w(TAG, "原文件删除失败，但恢复文件已创建")
                // 即使原文件删除失败，也标记为已恢复，避免重复显示
                markFileAsRecovered(file)
            }
            
            // 通知系统媒体扫描
            notifyMediaScanner(finalTargetFile.absolutePath)
            
            Log.d(TAG, "从文件路径恢复成功: 原文件名='${file.name}' -> 恢复文件名='${finalTargetFile.name}' -> 路径='${finalTargetFile.absolutePath}'")
            return finalTargetFile.absolutePath
            
        } catch (e: Exception) {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete()
            }
            // 清理可能创建的目标文件
            if (finalTargetFile.exists()) {
                finalTargetFile.delete()
            }
            throw Exception("原子恢复操作失败: ${e.message}")
        }
    }
    
    /**
     * 从 MediaStore 恢复
     */
    private fun recoverFromMediaStore(file: RecoverableFile): String {
        if (context == null) {
            throw Exception("Context为空")
        }
        
        val contentResolver = context.contentResolver
        
        // 检查文件ID是否为数字
        val fileId = try {
            file.id.toLong()
        } catch (e: NumberFormatException) {
            throw Exception("无效的文件ID: ${file.id}")
        }
        
        // 构建 ContentUri
        val uri = android.content.ContentUris.withAppendedId(
            MediaStore.Files.getContentUri("external"),
            fileId
        )
        
        try {
            // 如果是 Android 11+，尝试从回收站恢复
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                // 使用清理后的文件名更新 MediaStore
                val cleanFileName = FileUtils.cleanDeletedFileName(file.name)
                val values = android.content.ContentValues().apply {
                    put(MediaStore.Files.FileColumns.IS_TRASHED, 0)
                    put(MediaStore.Files.FileColumns.DISPLAY_NAME, cleanFileName)
                }
                val updatedRows = contentResolver.update(uri, values, null, null)
                if (updatedRows > 0) {
                    markFileAsRecovered(file)
                    Log.d(TAG, "从 MediaStore 回收站恢复成功: 原文件名='${file.name}' -> 恢复文件名='$cleanFileName'")
                    return file.path
                }
            }
            
            // 如果直接恢复失败，尝试复制文件
            contentResolver.openInputStream(uri)?.use { inputStream ->
                val recoveryDir = File("/storage/emulated/0/RecoveredFiles")
                if (!recoveryDir.exists()) {
                    recoveryDir.mkdirs()
                }
                
                // 使用清理后的文件名作为恢复后的文件名
                val cleanFileName = FileUtils.cleanDeletedFileName(file.name)
                val targetFile = File(recoveryDir, cleanFileName)
                
                // 如果目标文件已存在，添加数字后缀避免覆盖
                val finalTargetFile = getUniqueFileName(targetFile)
                
                finalTargetFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
                
                markFileAsRecovered(file)
                notifyMediaScanner(finalTargetFile.absolutePath)
                
                Log.d(TAG, "通过复制从 MediaStore 恢复成功: 原文件名='${file.name}' -> 恢复文件名='${finalTargetFile.name}' -> 路径='${finalTargetFile.absolutePath}'")
                return finalTargetFile.absolutePath
            }
            
            throw Exception("无法打开文件流")
            
        } catch (e: Exception) {
            Log.e(TAG, "MediaStore 恢复失败", e)
            throw Exception("MediaStore 恢复失败: ${e.message}")
        }
    }
    
    /**
     * 获取唯一的文件名，如果文件已存在则添加数字后缀
     */
    private fun getUniqueFileName(originalFile: File): File {
        if (!originalFile.exists()) {
            return originalFile
        }
        
        val parentDir = originalFile.parentFile ?: return originalFile
        val nameWithoutExt = originalFile.nameWithoutExtension
        val extension = originalFile.extension
        
        var counter = 1
        var newFile: File
        
        do {
            val newName = if (extension.isNotEmpty()) {
                "${nameWithoutExt}_$counter.$extension"
            } else {
                "${nameWithoutExt}_$counter"
            }
            newFile = File(parentDir, newName)
            counter++
        } while (newFile.exists() && counter < 1000) // 防止无限循环
        
        return newFile
    }
    
    /**
     * 标记文件为已恢复
     */
    private fun markFileAsRecovered(file: RecoverableFile) {
        recoveredFileIds.add(file.id)
    }
    
    /**
     * 通知系统媒体扫描器
     */
    private fun notifyMediaScanner(filePath: String) {
        try {
            if (context != null) {
                val intent = android.content.Intent(android.content.Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
                intent.data = android.net.Uri.fromFile(File(filePath))
                context.sendBroadcast(intent)
            }
        } catch (e: Exception) {
            Log.w(TAG, "媒体扫描通知失败", e)
        }
    }
    
    /**
     * 兼容性查询方法，支持 Bundle 参数
     */
    private fun queryTrashedWithBundleCompat(
        contentResolver: ContentResolver,
        uri: android.net.Uri,
        projection: Array<String>,
        queryArgs: Bundle
    ): Cursor? {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                contentResolver.query(uri, projection, queryArgs, null)
            } else {
                // 降级到普通查询
                contentResolver.query(uri, projection, null, null, null)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Bundle 查询失败，尝试普通查询", e)
            try {
                contentResolver.query(uri, projection, null, null, null)
            } catch (e2: Exception) {
                Log.e(TAG, "所有查询方式都失败", e2)
                null
            }
        }
    }
    
    suspend fun deleteFilePermanently(file: RecoverableFile): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (context == null) {
                    throw Exception("Context为空，无法删除文件")
                }
                
                val contentResolver = context.contentResolver
                
                // 方法1：如果文件路径存在，直接删除
                val sourceFile = File(file.path)
                if (sourceFile.exists()) {
                    val deleted = sourceFile.delete()
                    if (deleted) {
                        markFileAsRecovered(file) // 从列表中移除
                        Log.d(TAG, "直接删除文件成功: ${file.name}")
                        return@withContext true
                    }
                }
                
                // 方法2：通过 MediaStore 删除
                val fileId = try {
                    file.id.toLong()
                } catch (e: NumberFormatException) {
                    Log.w(TAG, "无效的文件ID，无法通过 MediaStore 删除: ${file.id}")
                    return@withContext false
                }
                
                val uri = android.content.ContentUris.withAppendedId(
                    MediaStore.Files.getContentUri("external"),
                    fileId
                )
                
                val deletedRows = contentResolver.delete(uri, null, null)
                if (deletedRows > 0) {
                    markFileAsRecovered(file) // 从列表中移除
                    Log.d(TAG, "通过 MediaStore 删除成功: ${file.name}")
                    return@withContext true
                }
                
                Log.w(TAG, "文件删除失败: ${file.name}")
                return@withContext false
                
            } catch (e: Exception) {
                Log.e(TAG, "永久删除文件失败", e)
                return@withContext false
            }
        }
    }
}


