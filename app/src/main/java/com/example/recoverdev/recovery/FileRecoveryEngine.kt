package com.example.recoverdev.recovery

import android.content.Context
import android.content.ContentResolver
import android.database.Cursor
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import com.example.recoverdev.data.model.FileType
import com.example.recoverdev.data.model.RecoverableFile
import com.example.recoverdev.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

class FileRecoveryEngine(
    private val context: Context? = null,
    private val onScanPathUpdate: ((String) -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "FileRecoveryEngine"
        // Cache for recovered files, to avoid duplicate display
        private val recoveredFileIds = mutableSetOf<String>()
    }
    
    suspend fun scanDeletedFiles(fileType: FileType): List<RecoverableFile> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting scan for deleted files in recycle bin, type: $fileType")
                
                val allFiles = mutableSetOf<RecoverableFile>()
                
                if (context == null) {
                    Log.w(TAG, "Context is null, cannot scan")
                    return@withContext emptyList()
                }
                
                // Merge methods 1 and 2: System database scan
                val systemDatabaseFiles = scanSystemDatabase()
                allFiles.addAll(systemDatabaseFiles)
                Log.d(TAG, "System database scan found ${systemDatabaseFiles.size} files")
                
                // Merge methods 3 and 4: File system location scan
                val fileSystemFiles = scanFileSystemLocations()
                allFiles.addAll(fileSystemFiles)
                Log.d(TAG, "File system location scan found ${fileSystemFiles.size} files")
                
                // New: Scan special folders (thumbnails, hidden files, etc.)
                val specialFolderScanner = SpecialFolderScanner(context, onScanPathUpdate)
                val specialFolderFiles = specialFolderScanner.scanSpecialFolders(fileType)
                allFiles.addAll(specialFolderFiles)
                Log.d(TAG, "Special folder scan found ${specialFolderFiles.size} files")
                
                // Method 6: System-level deep scan
                val systemScanner = SystemLevelScanner(context, onScanPathUpdate)
                val systemFiles = systemScanner.performDeepScan(fileType)
                allFiles.addAll(systemFiles)
                Log.d(TAG, "System-level scan found ${systemFiles.size} files")
                
                // Deduplicate: based on normalized path and size
                val uniqueFiles = allFiles.distinctBy { "${normalizePath(it.path)}_${it.size}" }
                
                // Filter by file type - use actual file type instead of recalculating
                val filteredFiles = uniqueFiles.filter { file ->
                    val shouldInclude = when (fileType) {
                        FileType.PHOTO -> file.type == FileType.PHOTO
                        FileType.VIDEO -> file.type == FileType.VIDEO
                        FileType.AUDIO -> file.type == FileType.AUDIO
                        FileType.OTHER -> file.type == FileType.OTHER && !isTempOrCacheFile(file.name, file.path)
                    }
                    
                    // Add debug logs, especially for files containing _temp
                    if (file.name.contains("_temp") || file.name.contains("_deleted")) {
                        Log.d(TAG, "Filter check: File='${file.name}', Actual type=${file.type}, Requested type=$fileType, Should include=$shouldInclude")
                    }
                    
                    shouldInclude
                }
                
                Log.d(TAG, "Found ${allFiles.size} total files, ${uniqueFiles.size} after deduplication, ${filteredFiles.size} filtered $fileType files")
                return@withContext filteredFiles
                
            } catch (e: Exception) {
                Log.e(TAG, "Error during scan", e)
                throw Exception("Scan failed: ${e.message}")
            }
        }
    }
    
    /**
     * Merged method: Scan system databases (MediaStore + ContentResolver)
     */
    private suspend fun scanSystemDatabase(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        // Original scanAllTrashedFiles logic
        files.addAll(scanAllTrashedFiles())
        
        // Original scanWithContentResolver logic
        files.addAll(scanWithContentResolver())
        
        return files
    }
    
    /**
     * Merged method: scan file system locations (recycle bin directories + vendor-specific locations)
     */
    private suspend fun scanFileSystemLocations(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        // Original scanFileSystemTrash logic
//        files.addAll(scanFileSystemTrash())
        
        // Original scanVendorSpecificTrash logic
        files.addAll(scanVendorSpecificTrash())
        
        return files
    }
    
    /**
     * Simple scan of all recycle bin files, without complex filtering
     */
    private suspend fun scanAllTrashedFiles(): List<RecoverableFile> {
        if (context == null) return emptyList()
        
        val files = mutableListOf<RecoverableFile>()
        
        // 1. Scan MediaStore.Files table (contains all file types)
        try {
            val contentResolver = context.contentResolver
            val uri = MediaStore.Files.getContentUri("external")
            
            val projection = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.DATA,
                MediaStore.Files.FileColumns.SIZE,
                MediaStore.Files.FileColumns.DATE_MODIFIED,
                MediaStore.Files.FileColumns.MIME_TYPE
            )
            
            // Add IS_TRASHED column (if supported)
            val projectionWithTrash = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                projection + MediaStore.Files.FileColumns.IS_TRASHED
            } else {
                projection
            }
            
            val cursor = when {
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R -> {
                    val args = Bundle().apply {
                        putInt("android:query-arg-match-trashed", 1)
                        putInt("android:query-arg-match-pending", 1)
                        // No MIME type restriction, scan all files
                        putString("android:query-arg-sql-sort-order", "${MediaStore.Files.FileColumns.DATE_MODIFIED} DESC")
                    }
                    Log.d(TAG, "Using Android 11+ Bundle query")
                    queryTrashedWithBundleCompat(contentResolver, uri, projectionWithTrash, args)
                }
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q -> {
                    Log.d(TAG, "Using Android 10 IS_TRASHED query")
                    contentResolver.query(
                        uri,
                        projectionWithTrash,
                        "${MediaStore.Files.FileColumns.IS_TRASHED} = ?",
                        arrayOf("1"),
                        "${MediaStore.Files.FileColumns.DATE_MODIFIED} DESC"
                    )
                }
                else -> {
                    Log.d(TAG, "Android 9 and below do not support recycle bin queries")
                    null
                }
            }
            
            cursor?.use { c ->
                Log.d(TAG, "Found ${c.count} records")
                
                val dataColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA)
                val nameColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME)
                val sizeColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.SIZE)
                val dateColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_MODIFIED)
                val idColumn = c.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
                val trashedColumn = c.getColumnIndex(MediaStore.Files.FileColumns.IS_TRASHED)
                
                while (c.moveToNext()) {
                    val filePath = c.getString(dataColumn) ?: continue
                    val fileName = c.getString(nameColumn) ?: File(filePath).name
                    val isTrashed = if (trashedColumn >= 0) {
                        c.getInt(trashedColumn) == 1
                    } else {
                        true // For versions that don't support IS_TRASHED, assume all are recycle bin files
                    }
                    
                    Log.d(TAG, "Checking file: $fileName, path: $filePath, trashed: $isTrashed")
                    
                    if (isTrashed) {
                        onScanPathUpdate?.invoke("$fileName")
                        delay(50)

                        val fileId = c.getLong(idColumn).toString()
                        if (!recoveredFileIds.contains(fileId)) {
                            val normPath = normalizePath(filePath)
                            val extension = getFileExtension(normPath)
                            val recoverable = RecoverableFile(
                                id = fileId,
                                name = fileName,
                                path = normPath,
                                size = c.getLong(sizeColumn),
                                dateModified = c.getLong(dateColumn) * 1000,
                                type = determineFileType(fileName),
                                format = extension.uppercase(),
                                mimeType = getMimeType(extension),
                                isRecovered = false,
                                recoveredPath = null,
                                thumbnailPath = generateThumbnailPath(normPath, fileName)
                            )
                            files.add(recoverable)
                            Log.d(TAG, "Adding recycle bin file: $fileName")
                        } else {
                            Log.d(TAG, "Skipping already recovered file: $fileName")
                        }
                    }
                }
            } ?: Log.w(TAG, "empty")
            
        } catch (e: Exception) {
            Log.e(TAG, "MediaStore.Files file", e)
        }
        
        // 2. If MediaStore scan results are empty, try scanning file system recycle bin directories
        if (files.isEmpty()) {
            Log.d(TAG, "MediaStore scan returned no results, trying file system scan")
            files.addAll(scanFileSystemTrash())
        }
        
        Log.d(TAG, "Total found ${files.size} recycle bin files")
        return files
    }
    
    /**
     * Scan file system recycle bin directories
     */
    private suspend fun scanFileSystemTrash(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        val storagePaths = listOf(
            "/storage/emulated/0",
            "/storage/emulated/1", 
            "/sdcard",
            "/mnt/sdcard"
        )
        
        val trashPatterns = listOf(
            ".Trash-",
            ".trash",
            ".Trash",
            "Recycle Bin",
            ".RecycleBin",
            "Trash",
            ".recently-deleted",
            "Recently Deleted",
            ".FileManagerRecycler"
        )
        
        storagePaths.forEach { storagePath ->
            try {
                val storageDir = File(storagePath)
                if (storageDir.exists() && storageDir.canRead()) {
                    onScanPathUpdate?.invoke(storagePath)
                    delay(50)
                    
                    storageDir.listFiles()?.forEach { subDir ->
                        if (subDir.isDirectory) {
                            val dirName = subDir.name
                            val isTrashDir = trashPatterns.any { pattern ->
                                when {
                                    pattern == ".Trash-" -> dirName.contains(".Trash-")
                                    else -> dirName == pattern
                                }
                            }
                            
                            if (isTrashDir && subDir.canRead()) {
                                Log.d(TAG, "Found trash directory: ${subDir.absolutePath}")
                                scanDirectoryRecursively(subDir, files, maxDepth = 10, maxFiles = 500)
                            }
                        }
                    }
                    
                    // Also scan some fixed sub-paths
                    val fixedSubPaths = listOf(
                        ".local/share/Trash/files",
                        "Android/data/com.android.documentsui/files/Trash"
                    )
                    
                    fixedSubPaths.forEach { subPath ->
                        val dir = File(storageDir, subPath)
                        if (dir.exists() && dir.canRead()) {
                            Log.d(TAG, "Scanning fixed trash path: ${dir.absolutePath}")
                            scanDirectoryRecursively(dir, files, maxDepth = 10, maxFiles = 500)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "Cannot access storage path: $storagePath - ${e.message}")
            }
        }
        
        return files
    }
    
    /**
     * Recursively scan directory
     */
    private suspend fun scanDirectoryRecursively(
        dir: File,
        files: MutableList<RecoverableFile>,
        maxDepth: Int = 10,
        currentDepth: Int = 0,
        maxFiles: Int = 500
    ) {
        if (currentDepth >= maxDepth || files.size >= maxFiles) return
        
        onScanPathUpdate?.invoke(dir.absolutePath)
        delay(50)
        
        try {
            dir.listFiles()?.forEach { file ->
                when {
                    file.isFile -> {
                        onScanPathUpdate?.invoke("${file.name}")
                        delay(50)

                        val normPath = normalizePath(file.absolutePath)
                        val extension = getFileExtension(file.name)
                        val recoverable = RecoverableFile(
                            id = UUID.randomUUID().toString(),
                            name = file.name,
                            path = normPath,
                            size = file.length(),
                            dateModified = file.lastModified(),
                            type = determineFileType(file.name), // Use correct file type determination
                            format = extension.uppercase(),
                            mimeType = getMimeType(extension),
                            isRecovered = false,
                            recoveredPath = null,
                            thumbnailPath = null
                        )
                        files.add(recoverable)
                        Log.d(TAG, "File system found: ${file.name}, type=${recoverable.type}, MIME=${recoverable.mimeType}")
                    }
                    file.isDirectory && currentDepth < maxDepth -> {
                        scanDirectoryRecursively(file, files, maxDepth, currentDepth + 1, maxFiles)
                    }
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "Failed to scan directory: ${dir.path} - ${e.message}")
        }
    }
    
    private suspend fun scanWithContentResolver(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        try {
            Log.d(TAG, "Starting ContentResolver scan")
            
            // Try different URIs and query methods
            val uris = listOf(
                MediaStore.Files.getContentUri("external"),
                MediaStore.Files.getContentUri("internal")
            )
            
            for (uri in uris) {
                try {
                    context?.contentResolver?.query(
                        uri,
                        null,
                        null,
                        null,
                        null
                    )?.use { cursor ->
                        Log.d(TAG, "URI $uri returned ${cursor.count} records")
                        
                        // Print all available column names
                        val columnNames = cursor.columnNames
                        Log.d(TAG, "Available columns: ${columnNames.joinToString(", ")}")
                        
                        var count = 0
                        while (cursor.moveToNext() && count < 10) { // Only process first 10 records as example
                            try {
                                val nameIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DISPLAY_NAME)
                                val pathIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DATA)
                                val sizeIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.SIZE)
                                
                                if (nameIndex >= 0 && pathIndex >= 0) {
                                    val name = cursor.getString(nameIndex) ?: "Unknown file"
                                    val path = cursor.getString(pathIndex) ?: ""
                                    val size = if (sizeIndex >= 0) cursor.getLong(sizeIndex) else 0L
                                    
                                    Log.d(TAG, "ContentResolver found file: $name, path: $path")
                                }
                            } catch (e: Exception) {
                                Log.w(TAG, "Error processing ContentResolver record", e)
                            }
                            count++
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to query URI $uri", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "ContentResolver scan failed", e)
        }
        
        return files
    }

    private suspend fun scanVendorSpecificTrash(): List<RecoverableFile> {
        val files = mutableListOf<RecoverableFile>()
        
        try {
            Log.d(TAG, "Starting vendor-specific trash scan")
            
            // Define trash directory name patterns (without full paths)
            val trashDirNames = listOf(
                // Common trash directory names
                ".Trash-",           // Match .Trash-1000, .Trash-1001 etc
                ".Trash",
                ".trash",
                "Trash",
                "trash",
                ".recycle",
                "recycle",
                ".recycleBin",
                "recycle_bin",
                "Recycle Bin",
                ".deleted",
                ".Deleted",
                "Recently Deleted",
                ".recently-deleted",
                "recently_delete",
                
                // Xiaomi MIUI
                ".trashBin",
                ".mi_trash",
                ".MIUITrash",
                ".miTrash",
                ".xiaomiTrash",
                
                // Huawei EMUI/HarmonyOS
                ".hwRecycleBin",
                "HwRecycleBin",
                ".huawei_trash",
                
                // OPPO ColorOS
                ".oppoTrash",
                ".oppoTmp",
                ".oppo_recycle",
                ".colorosTrash",
                ".oppoRecycle",
                
                // Vivo FuntouchOS/OriginOS
                ".vivoTrash",
                ".vivo_recycle",
                ".vivoRecycle",
                ".vivoRecycleBin",
                
                // Samsung One UI
                ".samsung_trash",
                ".samsungTrash",
                ".sec_trash",
                
                // OnePlus OxygenOS
                ".oneplusTrash",
                ".oneplus_recycle",
                
                // Meizu Flyme
                ".meizu_trash",
                ".mzTrash",
                
                // Realme
                ".realmeRecycle",
                
                // Lenovo
                ".lenovo_trash",
                
                // Others
                ".FileManagerRecycler"
            )
            
            // Scan all storage locations
            val storagePaths = listOf(
                "/storage/emulated/0",
                "/storage/emulated/1", 
                "/data/media/0",
                "/sdcard"
            )
            
            // Recursively scan all directories to find trash directories
            storagePaths.forEach { storagePath ->
                try {
                    val storageDir = File(storagePath)
                    if (storageDir.exists() && storageDir.canRead()) {
                        onScanPathUpdate?.invoke(storagePath)
                        delay(50)
                        scanForTrashDirectories(storageDir, trashDirNames, files, maxDepth = 10)
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to scan storage path: $storagePath", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Vendor-specific trash scan failed", e)
        }
        
        return files
    }
    
    /**
     * Recursively scan directories to find trash directories
     */
    private suspend fun scanForTrashDirectories(
        dir: File,
        trashDirNames: List<String>,
        files: MutableList<RecoverableFile>,
        maxDepth: Int = 10,
        currentDepth: Int = 0,
        maxFiles: Int = 500
    ) {
        if (currentDepth >= maxDepth || files.size >= maxFiles) return
        
        try {
            dir.listFiles()?.forEach { subDir ->
                if (subDir.isDirectory && subDir.canRead()) {
                    val dirName = subDir.name
                    val dirPath = subDir.absolutePath
                    
                    // Check if directory name matches trash patterns
                    val isTrashDir = trashDirNames.any { pattern ->
                        when {
                            pattern == ".Trash-" -> dirName.contains(".Trash-")
                            else -> dirName == pattern || dirPath.contains("/$pattern/")
                        }
                    }
                    
                    if (isTrashDir) {
                        Log.d(TAG, "Found trash directory: ${subDir.absolutePath}")
                        scanDirectoryRecursively(subDir, files, maxDepth = 10)
                    } else {
                        // Continue recursive scanning of subdirectories
                        scanForTrashDirectories(subDir, trashDirNames, files, maxDepth, currentDepth + 1, maxFiles)
                    }
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "Failed to scan directory: ${dir.path} - ${e.message}")
        }
    }
    
    /**
     * Normalize paths, unify different root paths to standard format
     */
    private fun normalizePath(path: String): String {
        return path
            .replace("/storage/emulated/0/", "/sdcard/")
            .replace("/storage/sdcard0/", "/sdcard/")
            .replace("/storage/sdcard1/", "/sdcard/")
            .replace("/mnt/sdcard/", "/sdcard/")
            .replace("/external_sd/", "/sdcard/")
            .replace("//", "/") // Remove double slashes
    }
    
    /**
     * Get file extension, handle _temp suffix added by OPPO and other vendors
     */
    private fun getFileExtension(fileName: String): String {
        // Handle suffixes added by OPPO and other vendors when deleting files
        val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
        return cleanFileName.substringAfterLast('.', "")
    }

    /**
     * Get MIME type based on file extension
     */
    private fun getMimeType(extension: String): String? {
        return when (extension.lowercase()) {
            // Images
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "webp" -> "image/webp"
            "heic" -> "image/heic"
            "raw" -> "image/x-canon-cr2"
            "tiff" -> "image/tiff"
            "svg" -> "image/svg+xml"
            // Videos
            "mp4" -> "video/mp4"
            "avi" -> "video/x-msvideo"
            "mkv" -> "video/x-matroska"
            "mov" -> "video/quicktime"
            "wmv" -> "video/x-ms-wmv"
            "flv" -> "video/x-flv"
            "webm" -> "video/webm"
            "3gp" -> "video/3gpp"
            "m4v" -> "video/x-m4v"
            "mpg", "mpeg" -> "video/mpeg"
            "rm" -> "application/vnd.rn-realmedia"
            "rmvb" -> "application/vnd.rn-realmedia-vbr"
            // Audio
            "mp3" -> "audio/mpeg"
            "wav" -> "audio/wav"
            "flac" -> "audio/flac"
            "aac" -> "audio/aac"
            "ogg" -> "audio/ogg"
            "wma" -> "audio/x-ms-wma"
            "m4a" -> "audio/mp4"
            "opus" -> "audio/opus"
            "amr" -> "audio/amr"
            "mid", "midi" -> "audio/midi"
            // Others
            "pdf" -> "application/pdf"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "txt" -> "text/plain"
            "zip" -> "application/zip"
            "apk" -> "application/vnd.android.package-archive"
            else -> null
        }
    }
    
    /**
     * 判断文件是否为媒体文件（Images、Videos、Audio）
     */
    private fun isMediaFile(fileName: String): Boolean {
        val extension = getFileExtension(fileName).lowercase()
        val photoExtensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "heic", "raw", "tiff", "svg")
        val videoExtensions = listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp", "m4v", "mpg", "mpeg", "rm", "rmvb")
        val audioExtensions = listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus", "amr", "mid", "midi")
        val allMediaExtensions = photoExtensions + videoExtensions + audioExtensions
        return allMediaExtensions.contains(extension)
    }
    
    /**
     * Determine if file is temporary or cache file
     */
    private fun isTempOrCacheFile(fileName: String, filePath: String): Boolean {
        val lowerFileName = fileName.lowercase()
        val lowerFilePath = filePath.lowercase()
        
        // Only filter obvious system temp files, keep user documents
        val systemTempIndicators = listOf(".tmp", ".cache", "~")
        val hasSystemTempName = systemTempIndicators.any { indicator -> lowerFileName.endsWith(indicator) }
        
        // Only filter system-level temp directories, not user directories
        val systemTempPaths = listOf("/.cache/", "/cache/", "/data/local/tmp/", "/tmp/")
        val isInSystemTempPath = systemTempPaths.any { path -> lowerFilePath.contains(path) }
        
        // Filter obvious junk files
        val isJunkFile = lowerFileName.startsWith("thumbs.db") || 
                        lowerFileName.startsWith(".ds_store") ||
                        lowerFileName.endsWith(".log") && lowerFilePath.contains("/log/")
        
        return hasSystemTempName || isInSystemTempPath || isJunkFile
    }
    
    /**
     * Determine file type based on filename, handle special suffixes of deleted files
     */
    private fun determineFileType(fileName: String): FileType {
        // Use cleaned filename to get extension
        val cleanFileName = FileUtils.cleanDeletedFileName(fileName)
        val extension = cleanFileName.substringAfterLast('.', "").lowercase()
        
        val fileType = when {
            listOf("jpg", "jpeg", "png", "gif", "bmp", "webp", "heic", "raw", "tiff", "svg").contains(extension) -> FileType.PHOTO
            listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp", "m4v", "mpg", "mpeg", "rm", "rmvb").contains(extension) -> FileType.VIDEO
            listOf("mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus", "amr", "mid", "midi").contains(extension) -> FileType.AUDIO
            else -> FileType.OTHER
        }
        
        // Add debug log
        if (fileName != cleanFileName) {
            Log.d(TAG, "File type determination: original='$fileName', cleaned='$cleanFileName', extension='$extension', type=$fileType")
        }
        
        return fileType
    }

    /**
     * Generate thumbnail path (if file exists and is media file)
     */
    private fun generateThumbnailPath(filePath: String, fileName: String): String? {
        val file = File(filePath)
        if (!file.exists()) return null
        
        val extension = getFileExtension(fileName).lowercase()
        return when {
            listOf("jpg", "jpeg", "png", "gif", "bmp", "webp").contains(extension) -> filePath
            listOf("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "3gp").contains(extension) -> filePath
            else -> null
        }
    }
    
    suspend fun recoverFile(file: RecoverableFile): String {
        return withContext(Dispatchers.IO) {
            try {
                if (context == null) {
                    throw Exception("Context is null, cannot recover file")
                }
                
                // Method 1: If source file path exists, copy directly
                val sourceFile = File(file.path)
                if (sourceFile.exists() && sourceFile.canRead()) {
                    return@withContext recoverFromFilePath(sourceFile, file)
                }
                
                // Method 2: Recover through MediaStore ContentUri
                return@withContext recoverFromMediaStore(file)
                
            } catch (e: Exception) {
                Log.e(TAG, "File recovery failed", e)
                throw Exception("File recovery failed: ${e.message}")
            }
        }
    }
    
    /**
     * Recover from file path - atomic operation version
     */
    private fun recoverFromFilePath(sourceFile: File, file: RecoverableFile): String {
        // 创建恢复目录
        val recoveryDir = File("/storage/emulated/0/RecoveredFiles")
        if (!recoveryDir.exists()) {
            recoveryDir.mkdirs()
        }
        
        // Use cleaned filename as recovered filename
        val cleanFileName = FileUtils.cleanDeletedFileName(file.name)
        val targetFile = File(recoveryDir, cleanFileName)
        
        // If target file exists, add numeric suffix to avoid overwrite
        val finalTargetFile = getUniqueFileName(targetFile)
        
        // Create temp file to ensure atomic operation
        val tempFile = File(finalTargetFile.parentFile, "${finalTargetFile.name}.tmp")
        
        try {
            // Step 1: Copy to temp file first
            sourceFile.copyTo(tempFile, overwrite = true)
            
            // Step 2: Verify copy success
            if (!tempFile.exists() || tempFile.length() != sourceFile.length()) {
                throw Exception("File copy verification failed")
            }
            
            // Step 3: Atomically rename temp file to final file
            if (!tempFile.renameTo(finalTargetFile)) {
                throw Exception("Temp file rename failed")
            }
            
            // Step 4: Only delete original file and mark after successful recovery
            val sourceDeleted = sourceFile.delete()
            if (sourceDeleted) {
                markFileAsRecovered(file)
                Log.d(TAG, "Atomic recovery successful: original file deleted")
            } else {
                Log.w(TAG, "Original file deletion failed, but recovery file created")
                // Mark as recovered even if original deletion failed, avoid duplicate display
                markFileAsRecovered(file)
            }
            
            // Notify system media scanner
            notifyMediaScanner(finalTargetFile.absolutePath)
            
            Log.d(TAG, "File path recovery successful: original='${file.name}' -> recovered='${finalTargetFile.name}' -> path='${finalTargetFile.absolutePath}'")
            return finalTargetFile.absolutePath
            
        } catch (e: Exception) {
            // Clean up temp file
            if (tempFile.exists()) {
                tempFile.delete()
            }
            // Clean up possibly created target file
            if (finalTargetFile.exists()) {
                finalTargetFile.delete()
            }
            throw Exception("Atomic recovery operation failed: ${e.message}")
        }
    }
    
    /**
     * Recover from MediaStore
     */
    private fun recoverFromMediaStore(file: RecoverableFile): String {
        if (context == null) {
            throw Exception("Context is null")
        }
        
        val contentResolver = context.contentResolver
        
        // Check if file ID is numeric
        val fileId = try {
            file.id.toLong()
        } catch (e: NumberFormatException) {
            throw Exception("Invalid file ID: ${file.id}")
        }
        
        // Build ContentUri
        val uri = android.content.ContentUris.withAppendedId(
            MediaStore.Files.getContentUri("external"),
            fileId
        )
        
        try {
            // If Android 11+, try to recover from trash
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                // Update MediaStore with cleaned filename
                val cleanFileName = FileUtils.cleanDeletedFileName(file.name)
                val values = android.content.ContentValues().apply {
                    put(MediaStore.Files.FileColumns.IS_TRASHED, 0)
                    put(MediaStore.Files.FileColumns.DISPLAY_NAME, cleanFileName)
                }
                val updatedRows = contentResolver.update(uri, values, null, null)
                if (updatedRows > 0) {
                    markFileAsRecovered(file)
                    Log.d(TAG, "MediaStore trash recovery successful: original='${file.name}' -> recovered='$cleanFileName'")
                    return file.path
                }
            }
            
            // If direct recovery fails, try copying file
            contentResolver.openInputStream(uri)?.use { inputStream ->
                val recoveryDir = File("/storage/emulated/0/RecoveredFiles")
                if (!recoveryDir.exists()) {
                    recoveryDir.mkdirs()
                }
                
                // Use cleaned filename as recovered filename
                val cleanFileName = FileUtils.cleanDeletedFileName(file.name)
                val targetFile = File(recoveryDir, cleanFileName)
                
                // If target file exists, add numeric suffix to avoid overwrite
                val finalTargetFile = getUniqueFileName(targetFile)
                
                finalTargetFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
                
                markFileAsRecovered(file)
                notifyMediaScanner(finalTargetFile.absolutePath)
                
                Log.d(TAG, "通过复制Recover from MediaStore成功: 原文件名='${file.name}' -> 恢复文件名='${finalTargetFile.name}' -> 路径='${finalTargetFile.absolutePath}'")
                return finalTargetFile.absolutePath
            }
            
            throw Exception("Cannot open file stream")
            
        } catch (e: Exception) {
            Log.e(TAG, "MediaStore recovery failed", e)
            throw Exception("MediaStore recovery failed: ${e.message}")
        }
    }
    
    /**
     * Get unique filename, add numeric suffix if file exists
     */
    private fun getUniqueFileName(originalFile: File): File {
        if (!originalFile.exists()) {
            return originalFile
        }
        
        val parentDir = originalFile.parentFile ?: return originalFile
        val nameWithoutExt = originalFile.nameWithoutExtension
        val extension = originalFile.extension
        
        var counter = 1
        var newFile: File
        
        do {
            val newName = if (extension.isNotEmpty()) {
                "${nameWithoutExt}_$counter.$extension"
            } else {
                "${nameWithoutExt}_$counter"
            }
            newFile = File(parentDir, newName)
            counter++
        } while (newFile.exists() && counter < 1000) // Prevent infinite loop
        
        return newFile
    }
    
    /**
     * Mark file as recovered
     */
    private fun markFileAsRecovered(file: RecoverableFile) {
        recoveredFileIds.add(file.id)
    }
    
    /**
     * Notify system media scanner器
     */
    private fun notifyMediaScanner(filePath: String) {
        try {
            if (context != null) {
                val intent = android.content.Intent(android.content.Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
                intent.data = android.net.Uri.fromFile(File(filePath))
                context.sendBroadcast(intent)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Media scan notification failed", e)
        }
    }
    
    /**
     * Compatibility query method, supports Bundle parameters
     */
    private fun queryTrashedWithBundleCompat(
        contentResolver: ContentResolver,
        uri: android.net.Uri,
        projection: Array<String>,
        queryArgs: Bundle
    ): Cursor? {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                contentResolver.query(uri, projection, queryArgs, null)
            } else {
                // Fallback to normal query
                contentResolver.query(uri, projection, null, null, null)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Bundle query failed, trying normal query", e)
            try {
                contentResolver.query(uri, projection, null, null, null)
            } catch (e2: Exception) {
                Log.e(TAG, "All query methods failed", e2)
                null
            }
        }
    }
    
    suspend fun deleteFilePermanently(file: RecoverableFile): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (context == null) {
                    throw Exception("Context is null，无法删除文件")
                }
                
                val contentResolver = context.contentResolver
                
                // Method 1: If file path exists, delete directly
                val sourceFile = File(file.path)
                if (sourceFile.exists()) {
                    val deleted = sourceFile.delete()
                    if (deleted) {
                        markFileAsRecovered(file) // Remove from list
                        Log.d(TAG, "Direct file deletion successful: ${file.name}")
                        return@withContext true
                    }
                }
                
                // Method 2: Delete through MediaStore
                val fileId = try {
                    file.id.toLong()
                } catch (e: NumberFormatException) {
                    Log.w(TAG, "Invalid file ID, cannot delete through MediaStore: ${file.id}")
                    return@withContext false
                }
                
                val uri = android.content.ContentUris.withAppendedId(
                    MediaStore.Files.getContentUri("external"),
                    fileId
                )
                
                val deletedRows = contentResolver.delete(uri, null, null)
                if (deletedRows > 0) {
                    markFileAsRecovered(file) // Remove from list
                    Log.d(TAG, "MediaStore deletion successful: ${file.name}")
                    return@withContext true
                }
                
                Log.w(TAG, "File deletion failed: ${file.name}")
                return@withContext false
                
            } catch (e: Exception) {
                Log.e(TAG, "Permanent file deletion failed", e)
                return@withContext false
            }
        }
    }
}


