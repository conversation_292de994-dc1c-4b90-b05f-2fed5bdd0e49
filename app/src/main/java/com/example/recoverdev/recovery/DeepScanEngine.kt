package com.example.recoverdev.recovery

import android.util.Log
import com.example.recoverdev.data.FileType
import com.example.recoverdev.data.RecoveredFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.Date

/**
 * 深度扫描引擎 - 需要Root权限
 * 通过分析文件系统底层数据来恢复文件
 */
class DeepScanEngine {
    
    companion object {
        private const val TAG = "DeepScanEngine"
        
        // 常见文件头标识
        private val FILE_SIGNATURES = mapOf(
            "jpg" to listOf("FFD8FF"),
            "png" to listOf("89504E47"),
            "gif" to listOf("474946"),
            "mp4" to listOf("66747970"),
            "mp3" to listOf("494433", "FFFB"),
            "pdf" to listOf("25504446"),
            "zip" to listOf("504B0304")
        )
    }
    
    /**
     * 检查是否有Root权限
     */
    fun hasRootAccess(): Boolean {
        return try {
            val process = Runtime.getRuntime().exec("su -c 'echo test'")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val result = reader.readLine()
            process.waitFor()
            result == "test"
        } catch (e: Exception) {
            Log.d(TAG, "Root权限检查失败", e)
            false
        }
    }
    
    /**
     * 深度扫描存储设备
     */
    fun deepScanStorage(): Flow<RecoveredFile> = flow {
        if (!hasRootAccess()) {
            Log.w(TAG, "没有Root权限，无法进行深度扫描")
            return@flow
        }
        
        try {
            // 扫描主要存储分区
            val partitions = listOf(
                "/dev/block/mmcblk0p1", // 通常是用户数据分区
                "/dev/block/mmcblk0p2",
                "/data/data", // 应用数据目录
                "/sdcard" // 外部存储
            )
            
            partitions.forEach { partition ->
                scanPartition(partition).forEach { file ->
                    emit(file)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "深度扫描失败", e)
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * 扫描指定分区
     */
    private fun scanPartition(partitionPath: String): List<RecoveredFile> {
        val recoveredFiles = mutableListOf<RecoveredFile>()
        
        try {
            // 使用dd命令读取原始数据
            val command = "su -c 'dd if=$partitionPath bs=4096 count=1000 2>/dev/null | hexdump -C'"
            val process = Runtime.getRuntime().exec(command)
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            
            var line: String?
            val dataBuffer = StringBuilder()
            
            while (reader.readLine().also { line = it } != null) {
                line?.let { dataBuffer.append(it) }
            }
            
            process.waitFor()
            
            // 分析十六进制数据，查找文件签名
            val hexData = dataBuffer.toString()
            recoveredFiles.addAll(analyzeHexData(hexData, partitionPath))
            
        } catch (e: Exception) {
            Log.e(TAG, "扫描分区失败: $partitionPath", e)
        }
        
        return recoveredFiles
    }
    
    /**
     * 分析十六进制数据，查找文件签名
     */
    private fun analyzeHexData(hexData: String, sourcePath: String): List<RecoveredFile> {
        val recoveredFiles = mutableListOf<RecoveredFile>()
        
        FILE_SIGNATURES.forEach { (extension, signatures) ->
            signatures.forEach { signature ->
                var startIndex = 0
                while (true) {
                    val index = hexData.indexOf(signature, startIndex)
                    if (index == -1) break
                    
                    // 找到文件签名，尝试恢复文件信息
                    val recoveredFile = createRecoveredFileFromSignature(
                        extension, index, sourcePath
                    )
                    recoveredFiles.add(recoveredFile)
                    
                    startIndex = index + signature.length
                }
            }
        }
        
        return recoveredFiles
    }
    
    /**
     * 从文件签名创建RecoveredFile对象
     */
    private fun createRecoveredFileFromSignature(
        extension: String,
        position: Int,
        sourcePath: String
    ): RecoveredFile {
        val fileName = "recovered_${System.currentTimeMillis()}_$position.$extension"
        val fileType = FileType.fromExtension(extension)
        
        return RecoveredFile(
            originalPath = "$sourcePath:$position",
            fileName = fileName,
            fileSize = 0L, // 需要进一步分析确定文件大小
            fileType = fileType,
            deletedDate = null,
            recoveryConfidence = 0.7f, // 基于签名的恢复置信度
            isRecoverable = true
        )
    }
    
    /**
     * 从原始数据恢复文件
     */
    suspend fun recoverFromRawData(
        recoveredFile: RecoveredFile,
        outputPath: String
    ): Boolean {
        if (!hasRootAccess()) {
            Log.w(TAG, "没有Root权限，无法恢复原始数据")
            return false
        }
        
        return try {
            val pathParts = recoveredFile.originalPath.split(":")
            val sourcePath = pathParts[0]
            val position = pathParts[1].toInt()
            
            // 使用dd命令从指定位置提取数据
            val command = "su -c 'dd if=$sourcePath of=$outputPath bs=1 skip=$position count=1048576'"
            val process = Runtime.getRuntime().exec(command)
            val exitCode = process.waitFor()
            
            exitCode == 0
        } catch (e: Exception) {
            Log.e(TAG, "从原始数据恢复文件失败", e)
            false
        }
    }
    
    /**
     * 扫描SQLite数据库中的已删除记录
     */
    fun scanDeletedDatabaseRecords(): Flow<RecoveredFile> = flow {
        if (!hasRootAccess()) return@flow
        
        try {
            // 扫描常见的SQLite数据库文件
            val dbPaths = listOf(
                "/data/data/com.android.providers.media/databases/external.db",
                "/data/data/com.android.providers.media/databases/internal.db"
            )
            
            dbPaths.forEach { dbPath ->
                scanSQLiteDatabase(dbPath).forEach { file ->
                    emit(file)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "扫描数据库记录失败", e)
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * 扫描SQLite数据库
     */
    private fun scanSQLiteDatabase(dbPath: String): List<RecoveredFile> {
        val recoveredFiles = mutableListOf<RecoveredFile>()
        
        try {
            // 使用sqlite3命令查询已删除的媒体文件记录
            val command = "su -c 'sqlite3 $dbPath \"SELECT _data, _size, date_modified FROM files WHERE _data LIKE '%.jpg' OR _data LIKE '%.mp4' OR _data LIKE '%.mp3'\"'"
            val process = Runtime.getRuntime().exec(command)
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                line?.let { 
                    val parts = it.split("|")
                    if (parts.size >= 3) {
                        val filePath = parts[0]
                        val fileSize = parts[1].toLongOrNull() ?: 0L
                        val modifiedTime = parts[2].toLongOrNull() ?: 0L
                        
                        val file = java.io.File(filePath)
                        if (!file.exists()) { // 文件已被删除
                            val recoveredFile = RecoveredFile(
                                originalPath = filePath,
                                fileName = file.name,
                                fileSize = fileSize,
                                fileType = FileType.fromExtension(file.extension),
                                deletedDate = Date(modifiedTime * 1000),
                                recoveryConfidence = 0.8f,
                                isRecoverable = true
                            )
                            recoveredFiles.add(recoveredFile)
                        }
                    }
                }
            }
            
            process.waitFor()
            
        } catch (e: Exception) {
            Log.e(TAG, "扫描SQLite数据库失败: $dbPath", e)
        }
        
        return recoveredFiles
    }
}