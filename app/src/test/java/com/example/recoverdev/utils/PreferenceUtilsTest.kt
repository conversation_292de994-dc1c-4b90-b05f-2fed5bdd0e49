package com.example.recoverdev.utils

import com.example.recoverdev.ui.screen.GuidePageData
import org.junit.Test
import org.junit.Assert.*

/**
 * 引导页相关逻辑的基本测试
 */
class PreferenceUtilsTest {

    @Test
    fun testGuidePageDataCreation() {
        // 测试引导页数据类的创建是否正确
        val pageData = GuidePageData(
            imageRes = 1,
            titleRes = 2,
            descriptionRes = 3
        )
        
        assertEquals("Image res should match", 1, pageData.imageRes)
        assertEquals("Title res should match", 2, pageData.titleRes)
        assertEquals("Description res should match", 3, pageData.descriptionRes)
    }
    
    @Test
    fun testGuidePageDataEquality() {
        // 测试引导页数据类的相等性
        val pageData1 = GuidePageData(
            imageRes = 1,
            titleRes = 2,
            descriptionRes = 3
        )
        
        val pageData2 = GuidePageData(
            imageRes = 1,
            titleRes = 2,
            descriptionRes = 3
        )
        
        assertEquals("Same guide page data should be equal", pageData1, pageData2)
    }
}