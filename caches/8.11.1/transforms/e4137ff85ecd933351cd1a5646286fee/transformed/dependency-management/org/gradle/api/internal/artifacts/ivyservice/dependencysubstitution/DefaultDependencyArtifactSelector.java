/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gradle.api.internal.artifacts.ivyservice.dependencysubstitution;

import org.gradle.api.artifacts.DependencyArtifactSelector;

import javax.annotation.Nullable;

public class DefaultDependencyArtifactSelector implements DependencyArtifactSelector {
    private final String type;
    private final String extension;
    private final String classifier;

    public DefaultDependencyArtifactSelector(String type, @Nullable String extension, @Nullable String classifier) {
        this.type = type;
        this.classifier = classifier;
        this.extension = extension;
    }

    @Override
    public String getType() {
        return type;
    }

    @Nullable
    @Override
    public String getExtension() {
        return extension;
    }

    @Nullable
    @Override
    public String getClassifier() {
        return classifier;
    }
}
