/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.service.scopes;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Attached to a listener interface to indicate that its events are stateful.
 * <p>
 * The listener infrastructure will ensure that a listener of this type will either receive all events, or no events.
 * Currently, this is done by disallowing the registration and de-registration of a listener of this type once any events
 * have been fired.
 * <p>
 * As a benefit to forbidding mutation of listeners after events have been fired, the listener infrastructure can
 * concurrently notify {@link ParallelListener parallel} listeners of stateful events.
 *
 * @see Scope
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Inherited
public @interface StatefulListener {
}
