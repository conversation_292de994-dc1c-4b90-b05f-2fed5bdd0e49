/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.instrumentation.agent;

import org.gradle.internal.service.scopes.Scope;
import org.gradle.internal.service.scopes.ServiceScope;

/**
 * Initializes the instrumenting agent.
 */
@ServiceScope(Scope.Global.class)
public class AgentInitializer {
    private final AgentStatus agentStatus;

    public AgentInitializer(AgentStatus agentStatus) {
        this.agentStatus = agentStatus;
    }

    /**
     * Sets up the agent-based instrumentation if it is enabled for the process.
     */
    public void maybeConfigureInstrumentationAgent() {
        if (agentStatus.isAgentInstrumentationEnabled()) {
            DefaultClassFileTransformer.tryInstall();
        }
    }
}
