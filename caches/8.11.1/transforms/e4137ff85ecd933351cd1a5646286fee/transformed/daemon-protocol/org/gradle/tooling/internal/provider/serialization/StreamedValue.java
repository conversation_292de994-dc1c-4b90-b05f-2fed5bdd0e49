/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.tooling.internal.provider.serialization;

import java.io.Serializable;

/**
 * An object sent back to the client from a client provided {@link org.gradle.tooling.BuildAction} running in the build process.
 */
public class StreamedValue implements Serializable {
    private final SerializedPayload serializedModel;

    public StreamedValue(SerializedPayload serializedModel) {
        this.serializedModel = serializedModel;
    }

    public SerializedPayload getSerializedModel() {
        return serializedModel;
    }
}
