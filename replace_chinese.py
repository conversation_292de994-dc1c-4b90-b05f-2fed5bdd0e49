#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 中英文对照表
replacements = {
    # 日志消息
    "URI $uri 返回 ${cursor.count} 条记录": "URI $uri returned ${cursor.count} records",
    "可用列: ${columnNames.joinToString(\", \")}": "Available columns: ${columnNames.joinToString(\", \")}",
    "只处理前10条作为示例": "Only process first 10 records as example",
    "未知文件": "Unknown file",
    "ContentResolver 找到文件: $name, 路径: $path": "ContentResolver found file: $name, path: $path",
    "处理 ContentResolver 记录时出错": "Error processing ContentResolver record",
    "查询 URI $uri 失败": "Failed to query URI $uri",
    "ContentResolver 扫描失败": "ContentResolver scan failed",
    
    # 厂商特定回收站
    "开始厂商特定回收站扫描": "Starting vendor-specific trash scan",
    "定义回收站目录名称模式（不包含完整路径）": "Define trash directory name patterns (without full paths)",
    "通用回收站目录名": "Common trash directory names",
    "匹配 .Trash-1000, .Trash-1001 等": "Match .Trash-1000, .Trash-1001 etc",
    "小米 MIUI": "Xiaomi MIUI",
    "华为 EMUI/HarmonyOS": "Huawei EMUI/HarmonyOS",
    "三星 One UI": "Samsung One UI",
    "一加 OxygenOS": "OnePlus OxygenOS",
    "魅族 Flyme": "Meizu Flyme",
    "联想": "Lenovo",
    "其他": "Others",
    "扫描所有存储位置": "Scan all storage locations",
    "递归扫描所有目录，查找包含回收站名称的目录": "Recursively scan all directories to find trash directories",
    "扫描存储路径失败: $storagePath": "Failed to scan storage path: $storagePath",
    "厂商回收站扫描失败": "Vendor-specific trash scan failed",
    
    # 目录扫描
    "递归扫描目录，查找包含回收站名称的目录": "Recursively scan directories to find trash directories",
    "检查目录名是否匹配回收站模式": "Check if directory name matches trash patterns",
    "发现回收站目录: ${subDir.absolutePath}": "Found trash directory: ${subDir.absolutePath}",
    "继续递归扫描子目录": "Continue recursive scanning of subdirectories",
    
    # 路径和文件处理
    "标准化路径，将不同的根路径统一为标准格式": "Normalize paths, unify different root paths to standard format",
    "移除双斜杠": "Remove double slashes",
    "获取文件扩展名，处理 OPPO 等厂商添加的 _temp 后缀": "Get file extension, handle _temp suffix added by OPPO and other vendors",
    "处理 OPPO 等厂商在删除文件时添加的后缀": "Handle suffixes added by OPPO and other vendors when deleting files",
    "根据文件扩展名获取 MIME 类型": "Get MIME type based on file extension",
    "图片": "Images",
    "视频": "Videos", 
    "音频": "Audio",
    
    # 文件类型判断
    "判断文件是否为媒体文件（图片、视频、音频）": "Determine if file is media file (image, video, audio)",
    "判断文件是否为临时文件或缓存文件": "Determine if file is temporary or cache file",
    "只过滤明显的系统临时文件，保留用户文档": "Only filter obvious system temp files, keep user documents",
    "只过滤系统级临时目录，不过滤用户目录": "Only filter system-level temp directories, not user directories",
    "过滤明显的垃圾文件": "Filter obvious junk files",
    "防止无限循环": "Prevent infinite loop",
    "根据文件名确定文件类型，处理删除文件的特殊后缀": "Determine file type based on filename, handle special suffixes of deleted files",
    "使用清理后的文件名获取扩展名": "Use cleaned filename to get extension",
    "添加调试日志": "Add debug log",
    "文件类型判断: 原文件名='$fileName', 清理后='$cleanFileName', 扩展名='$extension', 类型=$fileType": "File type determination: original='$fileName', cleaned='$cleanFileName', extension='$extension', type=$fileType",
    
    # 文件恢复
    "生成缩略图路径（如果文件存在且是媒体文件）": "Generate thumbnail path (if file exists and is media file)",
    "Context为空，无法恢复文件": "Context is null, cannot recover file",
    "方法1：如果源文件路径存在，直接复制": "Method 1: If source file path exists, copy directly",
    "方法2：通过 MediaStore ContentUri 恢复": "Method 2: Recover through MediaStore ContentUri",
    "文件恢复失败": "File recovery failed",
    "文件恢复失败: ${e.message}": "File recovery failed: ${e.message}",
    "从文件路径恢复 - 原子操作版本": "Recover from file path - atomic operation version",
    "使用清理后的文件名作为恢复后的文件名": "Use cleaned filename as recovered filename",
    "如果目标文件已存在，添加数字后缀避免覆盖": "If target file exists, add numeric suffix to avoid overwrite",
    "创建临时文件，确保原子操作": "Create temp file to ensure atomic operation",
    "步骤1：先复制到临时文件": "Step 1: Copy to temp file first",
    "步骤2：验证复制是否成功": "Step 2: Verify copy success",
    "文件复制验证失败": "File copy verification failed",
    "步骤3：原子性重命名临时文件为最终文件": "Step 3: Atomically rename temp file to final file",
    "临时文件重命名失败": "Temp file rename failed",
    "步骤4：只有在文件成功恢复后才删除原文件和标记": "Step 4: Only delete original file and mark after successful recovery",
    "原子操作恢复成功: 原文件已删除": "Atomic recovery successful: original file deleted",
    "原文件删除失败，但恢复文件已创建": "Original file deletion failed, but recovery file created",
    "即使原文件删除失败，也标记为已恢复，避免重复显示": "Mark as recovered even if original deletion failed, avoid duplicate display",
    "通知系统媒体扫描": "Notify system media scanner",
    "从文件路径恢复成功: 原文件名='${file.name}' -> 恢复文件名='${finalTargetFile.name}' -> 路径='${finalTargetFile.absolutePath}'": "File path recovery successful: original='${file.name}' -> recovered='${finalTargetFile.name}' -> path='${finalTargetFile.absolutePath}'",
    "清理临时文件": "Clean up temp file",
    "清理可能创建的目标文件": "Clean up possibly created target file",
    "原子恢复操作失败: ${e.message}": "Atomic recovery operation failed: ${e.message}",
    
    # MediaStore 恢复
    "从 MediaStore 恢复": "Recover from MediaStore",
    "Context为空": "Context is null",
    "检查文件ID是否为数字": "Check if file ID is numeric",
    "无效的文件ID: ${file.id}": "Invalid file ID: ${file.id}",
    "构建 ContentUri": "Build ContentUri",
    "如果是 Android 11+，尝试从回收站恢复": "If Android 11+, try to recover from trash",
    "使用清理后的文件名更新 MediaStore": "Update MediaStore with cleaned filename",
    "从 MediaStore 回收站恢复成功: 原文件名='${file.name}' -> 恢复文件名='$cleanFileName'": "MediaStore trash recovery successful: original='${file.name}' -> recovered='$cleanFileName'",
    "如果直接恢复失败，尝试复制文件": "If direct recovery fails, try copying file",
    "通过复制从 MediaStore 恢复成功: 原文件名='${file.name}' -> 恢复文件名='${finalTargetFile.name}' -> 路径='${finalTargetFile.absolutePath}'": "MediaStore copy recovery successful: original='${file.name}' -> recovered='${finalTargetFile.name}' -> path='${finalTargetFile.absolutePath}'",
    "无法打开文件流": "Cannot open file stream",
    "MediaStore 恢复失败": "MediaStore recovery failed",
    "MediaStore 恢复失败: ${e.message}": "MediaStore recovery failed: ${e.message}",
    
    # 其他功能
    "获取唯一的文件名，如果文件已存在则添加数字后缀": "Get unique filename, add numeric suffix if file exists",
    "标记文件为已恢复": "Mark file as recovered",
    "通知系统媒体扫描器": "Notify system media scanner",
    "媒体扫描通知失败": "Media scan notification failed",
    "兼容性查询方法，支持 Bundle 参数": "Compatibility query method, supports Bundle parameters",
    "降级到普通查询": "Fallback to normal query",
    "Bundle 查询失败，尝试普通查询": "Bundle query failed, trying normal query",
    "所有查询方式都失败": "All query methods failed",
    
    # 文件删除
    "Context为空，无法删除文件": "Context is null, cannot delete file",
    "方法1：如果文件路径存在，直接删除": "Method 1: If file path exists, delete directly",
    "从列表中移除": "Remove from list",
    "直接删除文件成功: ${file.name}": "Direct file deletion successful: ${file.name}",
    "方法2：通过 MediaStore 删除": "Method 2: Delete through MediaStore",
    "无效的文件ID，无法通过 MediaStore 删除: ${file.id}": "Invalid file ID, cannot delete through MediaStore: ${file.id}",
    "通过 MediaStore 删除成功: ${file.name}": "MediaStore deletion successful: ${file.name}",
    "文件删除失败: ${file.name}": "File deletion failed: ${file.name}",
    "永久删除文件失败": "Permanent file deletion failed",
    
    # 其他日志和注释
    "找到回收站目录: ${subDir.absolutePath}": "Found trash directory: ${subDir.absolutePath}",
    "也扫描一些固定的子路径": "Also scan some fixed sub-paths",
    "扫描固定回收站路径: ${dir.absolutePath}": "Scanning fixed trash path: ${dir.absolutePath}",
    "无法访问存储路径: $storagePath - ${e.message}": "Cannot access storage path: $storagePath - ${e.message}",
    "递归扫描目录": "Recursively scan directory",
    "使用正确的文件类型判断": "Use correct file type determination",
}

def replace_chinese_in_file(file_path):
    """替换文件中的中文内容"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 执行替换
    for chinese, english in replacements.items():
        content = content.replace(chinese, english)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Replaced Chinese text in {file_path}")

if __name__ == "__main__":
    file_path = "/Volumes/MobileSSD/projects/local/RecoverDev/app/src/main/java/com/example/recoverdev/recovery/FileRecoveryEngine.kt"
    replace_chinese_in_file(file_path)
